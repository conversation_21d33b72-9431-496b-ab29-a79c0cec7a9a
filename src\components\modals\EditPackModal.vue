<script lang="ts">
import { defineComponent, ref, onMounted, PropType } from 'vue';
import { storeToRefs } from 'pinia';
import { productStore } from 'src/stores/core/productStore';
import { packStore } from 'src/stores/core/packStore';
import { useQuasar } from 'quasar';
import { toRefs } from 'vue';

interface Product {
  id: number;
  name: string;
  price_vente: number;
  price_achat: number;
  category_id: number | null;
}

interface PackItem {
  id: number;
  product_id: number;
  price: number;
  quantity: number;
  category_id: number;
  product_name: string;
}

interface Form {
  products: PackItem[];
  name: string;
  category: string;
  tarif: string;
  description: string;
  total_price: string;
}

interface Pagination {
  page: number;
  rowsPerPage: number;
  rowsNumber: number;
}

interface Pack {
  id: number;
  name: string;
  description?: string;
  total_price: number;
  status?: string;
  duration?: number;
  category?: string;
  tarif?: number;
  products?: Product[];
}

export default defineComponent({
  name: "EditPackModal",
  components: {},
  props: {
    pack: {
      type: Object as PropType<Pack>,
      required: true,
    },
  },
  setup(props) {
    const $q = useQuasar();
    const dialog = ref(false);
    const showLoading = ref(false);
    const disabled = ref(false);
    const step = ref(1);
    const {pack} = toRefs(props);

    const prod_store = productStore();
    const { products, categories } = storeToRefs(productStore());
    const { getProducts } = prod_store;

    const pack_store = packStore();
    const { updatePack,getPackProducts,getPacks } = pack_store;

    const packs = ref<PackItem[]>([]);
    const selectedCategory = ref<number | null>(null);
    const filteredProducts = ref<Product[]>([]);
    const productName = ref("");
    const pagination = ref<Pagination>({
      page: 1,
      rowsPerPage: 20,
      rowsNumber: 0,
    });

    let pack_name = ref("");
    let total_price = ref(0);

    const form = ref<Form>({
      products: packs.value,
      name: "",
      category: "",
      tarif: "",
      description: "",
      total_price: "",
    });

    const openDialog = async () => {
      showLoading.value = true;
      disabled.value = true;


      try {
        const resProd = await getPackProducts(pack.value.id);
        if (resProd.success) {
          pack.value.products = resProd.result;
        }
        console.log("pack.value.products",pack.value.products);
        console.log("pack",pack.value);

        packs.value = pack.value.products?.map(product => ({
          id: product.id,
          product_id: product.id,
          price: product.price_vente,
          quantity: 1,
          category_id: product.category_id!,
          product_name: product.name,
        })) || [];

        form.value.name = props.pack.name;
        form.value.category = props.pack.category || "";
        form.value.tarif = props.pack.tarif?.toString() || "";
        form.value.description = props.pack.description || "";
        form.value.total_price = props.pack.total_price.toString();
        total_price.value = props.pack.total_price;

        const res = await getProducts({ page: 1, limit: 50 });
        if (res.success) {
          pagination.value.rowsNumber = res.result.total;
          pagination.value.page = res.result.current_page;
          pagination.value.rowsPerPage = res.result.per_page;
          products.value = res.result.data;
          filterProducts();
          dialog.value = true;
        }
      } catch (error) {
        console.log("Error : ", error);
        $q.notify({
          color: "red",
          type: 'negative',
          icon: "error",
          position: 'top-right',
          progress: true,
          timeout: 3500,
          message: "Erreur lors du chargement des produits"
        });
      } finally {
        showLoading.value = false;
        disabled.value = false;
      }
    };

    const filterProducts = () => {
      filteredProducts.value = products.value.filter((product) =>
        (selectedCategory.value === null || product.category_id === selectedCategory.value) &&
        (productName.value === "" || product.name.toLowerCase().includes(productName.value.toLowerCase()))
      );
    };

    const addProductToPack = (product: Product) => {
      const existingProduct = packs.value.find((pack) => pack.product_id === product.id);
      if (existingProduct) {
        existingProduct.quantity += 1;
      } else {
        packs.value.push({
          id: packs.value.length + 1,
          product_id: product.id,
          price: product.price_vente,
          quantity: 1,
          category_id: Number(product.category_id),
          product_name: product.name,
        });
      }
      updateTotalPrice();
    };

    const isProductSelected = (productId: number) => {
      return packs.value.some((pack) => pack.product_id === productId);
    };

    const removeProductFromPack = (product: Product) => {
      const existingProduct = packs.value.find((pack) => pack.product_id === product.id);
      if (existingProduct) {
        packs.value = packs.value.filter((pack) => pack.id !== existingProduct.id);
        updateTotalPrice();
      }
    };

    const remove_pack = (item: PackItem) => {
      packs.value = packs.value.filter((pack) => pack.id !== item.id);
      updateTotalPrice();
    };

    const updateTotalPrice = () => {
      total_price.value = packs.value.reduce((total, item) => total + (item.price * item.quantity), 0);
      form.value.total_price = total_price.value.toString();

      const nbre_days = 31 * 12;
      let tarif = total_price.value / nbre_days;
      tarif = Math.ceil(tarif);
      form.value.tarif = tarif.toFixed(2);
    };

    const nextStep = () => {
      if (step.value === 1) {
        step.value = 2;
      }
    };

    const prevStep = () => {
      if (step.value === 2) {
        step.value = 1;
      }
    };

    const onRequest = async (page: number) => {
      showLoading.value = true;
      pagination.value.page = page;

      const res = await getProducts({
        page: pagination.value.page,
        limit: pagination.value.rowsPerPage,
      });

      if (res.success) {
        showLoading.value = false;
        pagination.value.rowsNumber = res.result.total;
        pagination.value.page = res.result.current_page;
        pagination.value.rowsPerPage = res.result.per_page;
        products.value = res.result.data;
        filterProducts();
      }
    };

    const onSubmit = async () => {
      disabled.value = true;
      showLoading.value = true;
      try {
        let items = packs.value.map(element => ({
          product_id: element.product_id,
          quantity: element.quantity,
          price: element.price,
        }));
        form.value.products = items as any[];
        let payload = {
          pack_id: props.pack.id,
          ...form.value
        }
        const res = await updatePack(payload);
        if (res.success) {
          $q.notify({
            color: "green",
            type: 'positive',
            icon: "cloud_done",
            position: 'center',
            progress: true,
            timeout: 3500,
            message: res.message
          });
          onReset();
          await getPacks();
          dialog.value = true;
        } else {
          $q.notify({
            color: "red-4",
            type: 'negative',
            icon: "error",
            position: 'top-right',
            progress: true,
            timeout: 3500,
            message: res.message
          });
        }
      } catch (error) {
        console.log("Error : ", error);
        $q.notify({
          color: "red-4",
          type: 'negative',
          icon: "error",
          position: 'top-right',
          progress: true,
          timeout: 3500,
          message: "Erreur lors de la mise à jour du carnet"
        });
      } finally {
        showLoading.value = false;
        disabled.value = false;
      }
    };

    const onReset = () => {
      packs.value = [];
      form.value = {
        products: packs.value,
        name: "",
        category: "",
        tarif: "",
        description: "",
        total_price: "",
      };
      total_price.value = 0;
      step.value = 1;
    };

    onMounted(async () => {
      if (props.pack) {
        packs.value = props.pack.products?.map(product => ({
          id: product.id,
          product_id: product.id,
          price: product.price_vente,
          quantity: 1,
          category_id: product.category_id!,
          product_name: product.name,
        })) || [];
        form.value.name = props.pack.name;
        form.value.category = props.pack.category || "";
        form.value.tarif = props.pack.tarif?.toString() || "";
        form.value.description = props.pack.description || "";
        form.value.total_price = props.pack.total_price.toString();
        total_price.value = props.pack.total_price;
        updateTotalPrice();
      }
    });

    return {
      dialog, form, onSubmit, onReset,
      products, remove_pack, categories, packs, addProductToPack, selectedCategory, pagination,
      filteredProducts, showLoading, disabled, step, nextStep, prevStep, productName, isProductSelected,
      filterProducts, updateTotalPrice, onRequest, openDialog, removeProductFromPack,
    };
  }
});
</script>

<template>
  <div class="row">
    <q-btn flat color="primary" size="sm" icon="edit"  @click="openDialog" :loading="showLoading" />
    <q-dialog v-model="dialog" persistent maximized transition-show="slide-up" transition-hide="slide-down">
      <q-card class="my-card">
        <q-form @submit="onSubmit" @reset="onReset">
          <q-toolbar class="bg-primary text-white">
            <q-toolbar-title>
              {{ step === 1 ? 'Sélectionner les Produits' : 'Modifier les Quantités' }}
            </q-toolbar-title>
            <q-btn flat round dense icon="close" class="q-mr-xs" @click="dialog = false" />
            <q-btn label="Suivant" color="positive" type="button" @click="nextStep" v-if="step === 1" />
            <q-btn label="Enregistrer" color="positive" type="submit" v-if="step === 2" />
          </q-toolbar>
          <q-card-section>
            <div v-if="step === 1">
              <div class="row q-col-gutter-md q-mt-sm q-pb-sm">
                <div class="col col-md-6 col-sm-12 col-xs-12">
                  <q-select filled v-model="selectedCategory" :options="categories" label="Catégorie de produit"
                    hint="Catégorie" lazy-rules aria-placeholder="Catégorie" option-label="name" option-value="id"
                    map-options emit-value clearable @update:model-value="filterProducts" />
                </div>
                <div class="col col-md-6 col-sm-12 col-xs-12">
                  <q-input v-model="productName" type="text" label="Entrez le nom du produit"
                    hint="Entrez le nom du produit" lazy-rules aria-placeholder="Entrez le nom du produit"
                    @update:model-value="filterProducts" clearable />
                </div>
                <div class="col col-md-12 col-sm-12 col-xs-12 q-mt-sm">
                  <q-pagination v-model="pagination.page" color="primary"
                    :max="Math.ceil(pagination.rowsNumber / pagination.rowsPerPage)" boundary-links
                    @update:model-value="onRequest" />
                </div>
                <div class="col col-md-12 col-sm-12 col-xs-12 q-mt-sm">
                  <q-chip class="" color="negative" text-color="white" v-for="(pack, i) in packs" :key="i">
                    <span class="q-ml-sm">{{ pack.product_name }}</span>
                  </q-chip>
                </div>
                <div class="col col-md-12 col-sm-12 col-xs-12">
                  <q-scroll-area style="width: 100%; height: 350px;">
                    <q-list bordered separator>
                      <q-slide-item v-for="product in filteredProducts" :key="product.id" clickable v-ripple
                        @click="addProductToPack(product)"
                        :class="{ 'selected-product': isProductSelected(product.id) }"
                        @right="removeProductFromPack(product)">
                        <template v-slot:left>
                          <q-icon name="done" />
                        </template>
                        <template v-slot:right>
                          <q-icon name="delete" />
                        </template>

                        <q-item>
                          <q-item-section>
                            <q-item-label>{{ product.name }}</q-item-label>
                            <q-item-label caption :class="{ 'text-white': isProductSelected(product.id) }">{{
                              product.price_achat }}
                              FCFA</q-item-label>
                          </q-item-section>
                        </q-item>
                      </q-slide-item>
                    </q-list>
                  </q-scroll-area>
                </div>
              </div>
              <q-pagination v-model="pagination.page" color="primary"
                :max="Math.ceil(pagination.rowsNumber / pagination.rowsPerPage)" boundary-links
                @update:model-value="onRequest" />
            </div>
            <div v-if="step === 2">
              <div class="row q-col-gutter-md q-mt-sm q-pb-sm">
                <div class="col col-md-8 col-sm-12 col-xs-12">
                  <q-input v-model="form.name" label="Nom du carnet" hint="Nom du carnet" lazy-rules
                    placeholder="Entrez le nom du pack" clearable
                  />
                </div>
                <div class="col col-md-4 col-sm-12 col-xs-12">
                  <q-input v-model="form.tarif" label="Tarif" hint="Tarif du carnet" lazy-rules
                    placeholder="Entrez le tarif du carnet" clearable
                  />
                </div>
                <div class="col col-md-12 col-sm-12 col-xs-12">
                  <q-scroll-area style="width: 100%; height: 350px;">
                    <q-list bordered separator>
                      <q-item v-for="(item, i) in packs" :key="i" clickable v-ripple>
                        <q-item-section>
                          <q-item-label>{{ item.product_name }}</q-item-label>
                          <q-item-label caption>{{ item.price }} FCFA x {{ item.quantity }}</q-item-label>
                        </q-item-section>
                        <q-item-section side>
                          <div class="d-flex" style="display: flex" >
                            <q-input filled v-model="item.quantity" label="Quantité" type="number"
                              placeholder="Quantité" @update:model-value="updateTotalPrice"
                            />
                            <q-btn color="negative" flat icon="delete" @click="remove_pack(item)" />
                          </div>
                        </q-item-section>
                      </q-item>
                    </q-list>
                  </q-scroll-area>
                </div>
              </div>
            </div>
          </q-card-section>
          <q-card-actions align="right">
            <q-btn flat label="Annuler" color="primary" @click="dialog = false" />
            <q-btn label="Précédent" color="primary" type="button" @click="prevStep" v-if="step === 2" />
            <q-btn label="Suivant" color="positive" type="button" @click="nextStep" v-if="step === 1" />
            <q-btn label="Enregistrer" color="positive" type="submit" v-if="step === 2" :disable="disabled" />
          </q-card-actions>
          <q-inner-loading :showing="showLoading">
            <q-spinner-gears size="50px" color="primary" />
          </q-inner-loading>
        </q-form>
      </q-card>
    </q-dialog>
  </div>
</template>



<style scoped>
.selected-product {
  background-color: #3271e6;
  border: 1px solid #00796b;
  color: white;
}
</style>
