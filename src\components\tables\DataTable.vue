<script lang="ts">
  import {defineComponent,ref, toRefs} from 'vue';

  interface Table{
    title: string;
    initialPagination?: any;
    filter?: string;
    headers?: any;
    items?: any;
    actions?: {
      icon: string;
      color: string;
      label?: string;
      handler: (row: any) => void;
    }[];
  }

  export default defineComponent({
    name: "DataTable",
    props: {
      table: {
        type: Object as () => Table,
        default: () => ({ initialPagination: {}, filter: "", headers: [], items: [], actions: [] }),
      },
    },
    setup(props){

      const table = toRefs(props.table);
      const initialPagination = ref(table.initialPagination);
      const filter = ref(table.filter);
      const headers = ref(table.headers);
      const items = ref(table.items);
      const actions = ref(table.actions);
      return {
        initialPagination, filter, headers, items, actions
      };
    }
  });

</script>
<template>

</template>
