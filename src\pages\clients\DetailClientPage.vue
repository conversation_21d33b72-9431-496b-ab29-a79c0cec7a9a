<script lang="ts">
import { defineComponent, ref, toRefs, onMounted } from 'vue';
import { get_amount_format, get_date_format, get_phone_format } from 'src/helpers/utils';
import { useRoute } from 'vue-router';
import { personalStore } from 'src/stores/core/personalStore';
import { storeToRefs } from 'pinia';
import BreadCrumb from 'src/layouts/BreadCrumb.vue';
import { Client } from 'src/models';

export default defineComponent({
  name: 'DetailClientPage',
  components: {
    BreadCrumb
  },
  setup() {
    const route = useRoute();
    const client_code = route.params.code as string;
    const store = personalStore();
    const { client } = storeToRefs(personalStore());
    const { get_client_byCode, getDetailClient } = store;
    let clientInfo = get_client_byCode(client_code);
    const collector_principal = ref(null) as any;

    const bread = {
      pageTitle: 'Gestion Client',
      subTitle: clientInfo?.nom +" "+ clientInfo?.prenoms
    };
    const tab = ref('infos');

    onMounted(async () => {
      if (clientInfo !== null && clientInfo !== undefined) {
        // console.log("clientInfo", clientInfo);
        await getDetailClient(clientInfo?.id);
        console.log("client details", client.value);
        if (client.value?.collector) {
          collector_principal.value = client.value?.collector.slice(0, -1);
        }
        // console.log("collector_principal", collector_principal.value);
      }
    });

    return {
      clientInfo, get_amount_format, get_date_format, client, get_phone_format, bread, tab, collector_principal
    }
  }
});
</script>
<template>
  <q-page class="q-pa-md">
    <div class="row q-col-gutter-md ">
      <BreadCrumb :bread="bread" />
      <div class="col col-md-4 col-sm-12 col-xs-12">
        <q-card class="my-card" flat bordered >
          <q-card-section>
            <div style="padding-left: 35%;" class="row items-center">
              <q-avatar size="100px" font-size="52px" color="primary" text-color="white" icon="person"
                class="q-mb-sm text-center" />
            </div>
            <div class="text-center " style="font-weight: bold; font-size: large;">{{ clientInfo?.nom }} {{
              clientInfo?.prenoms }} </div>
            <div style="padding-bottom: 50px;">
              <!-- <q-input :model-value="clientInfo?.user?.role?.name" type="text" label="Role" filled readonly /> -->
              <q-input :model-value="clientInfo?.code" type="text" label="Code Client" filled readonly />
              <q-input :model-value="get_phone_format(clientInfo?.phone)" type="text" label="Téléphone" filled
                readonly />
              <q-input :model-value="clientInfo?.email" type="text" label="Email" filled readonly />
              <q-input :model-value="clientInfo?.city?.name" type="text" label="Ville" filled readonly />
              <q-input :model-value="clientInfo?.quarter?.name" type="text" label="Quartier" filled readonly />
            </div>
          </q-card-section>
        </q-card>
      </div>
      <div class="col col-md-8 col-sm-12 col-xs-12">
        <q-card class="my-card" flat bordered >
          <q-card-section>
            <!-- <div class="text-h6">Détail du client</div> -->
            <q-tabs v-model="tab" class="text-primary " align="justify">
              <q-tab name="infos" icon="account_circle" label="INFORMATIONS" />
              <q-tab name="agency" icon="home" label="AGENCE" />
              <q-tab name="collector" icon="account_circle" label="COLLECTEUR PRINCIPAL" />
            </q-tabs>
          </q-card-section>
          <q-card-section>
            <q-tab-panels v-model="tab" animated>
              <q-tab-panel name="infos">
                <div class="text-h6">Détail du client</div>
                <div class="row q-col-gutter-sm">
                  <div class="col col-md-6 col-sm-12 col-xs-12">
                    <q-input filled :model-value="clientInfo?.nom" label="Nom" readonly />
                  </div>
                  <div class="col col-md-6 col-sm-12 col-xs-12">
                    <q-input filled :model-value="clientInfo?.prenoms" label="Prénoms" readonly />
                  </div>
                  <div class="col col-md-6 col-sm-12 col-xs-12">
                    <q-input filled :model-value="clientInfo?.phone" label="Téléphone" readonly />
                  </div>
                  <div class="col col-md-6 col-sm-12 col-xs-12">
                    <q-input filled :model-value="clientInfo?.email" label="Email" readonly />
                  </div>
                  <div class="col col-md-6 col-sm-12 col-xs-12">
                    <q-input filled :model-value="clientInfo?.profession" label="Profession" readonly />
                  </div>

                  <div class="col col-md-6 col-sm-12 col-xs-12">
                    <q-input filled :model-value="clientInfo?.city?.name" label="Ville de résidence" readonly />
                  </div>
                  <div class="col col-md-6 col-sm-12 col-xs-12">
                    <q-input filled :model-value="clientInfo?.quarter?.name" label="Quartier" readonly />
                  </div>
                  <div class="col col-md-6 col-sm-12 col-xs-12">
                    <q-input filled :model-value="get_date_format(clientInfo?.created_at)" label="Date Création"
                      readonly />
                  </div>
                </div>
              </q-tab-panel>
              <q-tab-panel name="agency">
                <div class="text-h6">AGENCE</div>
                <div class="row">
                  <div class="col col-md-12 col-sm-12 col-xs-12">
                    <q-input filled :model-value="clientInfo?.agency?.name" label="Nom agence" readonly />
                  </div>
                  <div class="col col-md-12 col-sm-12 col-xs-12">
                    <q-input filled :model-value="clientInfo?.agency?.city?.name" label="Ville de résidence" readonly />
                  </div>
                  <div class="col col-md-12 col-sm-12 col-xs-12">
                    <q-input filled :model-value="clientInfo?.agency?.quarter?.name" label="Quartier" readonly />
                  </div>
                  <div class="col col-md-12 col-sm-12 col-xs-12">
                    <q-input filled :model-value="clientInfo?.agency?.phone" label="Téléphone" readonly />
                  </div>
                  <div class="col col-md-12 col-sm-12 col-xs-12">
                    <q-input filled :model-value="clientInfo?.agency?.email" label="Email" readonly />
                  </div>
                </div>
              </q-tab-panel>
              <q-tab-panel name="collector">
                <div class="text-h6">Collecteur en charge</div>
                <div class="row">
                  <div class="col col-md-12 col-sm-12 col-xs-12">
                    <q-input filled :model-value="clientInfo?.collector?.[0].nom" label="Nom agent" readonly />
                  </div>
                  <div class="col col-md-12 col-sm-12 col-xs-12">
                    <q-input filled :model-value="clientInfo?.collector?.[0].prenoms" label="Prénoms agent" readonly />
                  </div>
                  <div class="col col-md-12 col-sm-12 col-xs-12">
                    <q-input filled :model-value="clientInfo?.collector?.[0].phone" label="Contact agent" readonly />
                  </div>
                  <div class="col col-md-12 col-sm-12 col-xs-12">
                    <q-input filled :model-value="clientInfo?.collector?.[0].email" label="Email agent" readonly />
                  </div>
                  <div class="col col-md-12 col-sm-12 col-xs-12">
                    <q-input filled :model-value="clientInfo?.collector?.[0].gender" label="Civilité agent" readonly />
                  </div>
                </div>
              </q-tab-panel>
            </q-tab-panels>
          </q-card-section>
        </q-card>
      </div>
    </div>
  </q-page>
</template>
