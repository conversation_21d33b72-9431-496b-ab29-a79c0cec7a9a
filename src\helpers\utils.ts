import { ref, computed } from 'vue';
import { api } from 'src/boot/axios';


export async function downloadFile(url_path: string) {
  try {
    // const response = await api.get(url_path, { responseType: 'blob' });
    const response = await fetch(url_path, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      }
    });
    const result = await response.json();
    const blob = new Blob([result], { type: 'application/xlsx' })
    const link: any = document.createElement('a')
    link.href = URL.createObjectURL(blob)
    link.download = url_path
    link.click()
    URL.revokeObjectURL(link.href)

  } catch (error) {
    console.error(error);
  }
}

export async function downloadFileExport(url: string) {
  fetch(url)
    .then(response => response.blob())
    .then(blob => {
      const url = window.URL.createObjectURL(new Blob([blob]));
      const a = document.createElement('a');
      a.style.display = 'none';
      a.href = url;
      a.download = 'file';
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
    })
    .catch(() => {
      console.error('Une erreur s\'est produite lors du téléchargement du fichier.');
    });
}


export function makeFileDownload(url_path: string) {
  let newTab = window.open() as Window;
  newTab?.document.write('<html><body><a id="downloadLink" href="' + url_path + '" download> Télécharger le fichier </a></body></html>');
  newTab?.document.close();

  // Attendez que la page soit chargée
  newTab.onload = function () {
    // Obtenez la balise <a>
    let downloadLink = newTab?.document.getElementById('downloadLink');
    // Simulez un clic sur la balise <a>
    downloadLink?.click();
    newTab?.document.close();
  };

}

export function get_role_byName(name: string): number{

  let role_id = 0;
  switch (name) {
    case "CHEF AGENCE":
      role_id = 3;
      break;
    case "AGENT SUPERVISEUR":
      role_id = 4;
      break;
  }
  return role_id;
}

export const get_date_format = (created_at: any) => {
  const date = new Date(created_at);
  const year = date.getFullYear();
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  const day = date.getDate().toString().padStart(2, '0');
  const hours = date.getHours().toString().padStart(2, '0');
  const minutes = date.getMinutes().toString().padStart(2, '0');
  const seconds = date.getSeconds().toString().padStart(2, '0');
  return `${day}/${month}/${year} ${hours}:${minutes}`;
}


export const get_amount_format = (amount: any)=>{
  return new Intl.NumberFormat('fr-FR', { style: 'currency', currency: 'XOF' }).format(amount);
}

export const get_phone_format = (phone: any)=>{
  if (phone !== null && phone !== undefined) {
    return phone.replace(/(\d{2})(\d{2})(\d{2})(\d{2})/, "(+228) $1 $2 $3 $4");
  }else{
    return phone;
  }
}

export const get_status_format = (status: string)=>{
  let text = "";
  let color = "";
  switch (status) {
    case "pending":
      text = "En attente";
      color = "orange";
      break;
    case "started":
      text = "Démarré";
      color = "blue";
      break;
    case "finished":
      text = "Terminé";
      color = "green";
      break;
    case "delivered":
      text = "Livré";
      color = "lightgreen";
      break;
    case "canceled":
      text = "Annulé";
      color = "red";
      break;
    default:
      text = "En attente";
      color = "orange";
      break;
  }
  return { text, color };
}










