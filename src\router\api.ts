export const api = {
  admin:{
    dashboard: "admin/analitycs",
    analitycs: {
      dashboard: "admin/analitycs",
      payments: "admin/analitycs/payments",
      carnet_inactifs: "admin/analitycs/carnets-inactifs",
      last_clients: "admin/analitycs/last-clients",
      subscriptions: "admin/analitycs/subscriptions",
      versements: "admin/analitycs/versements",
    },
    agency:{
      all: "admin/agencies",
      detail: "admin/agencies/details",
      create: "admin/agencies/create",
      update: "admin/agencies/update",
      add_responsable: "admin/agencies/add_responsable",
    },
    //GRH
    personals:{
      all: "admin/personals",
      detail: "admin/personals/details",
      add: "admin/personals/add",
      update: "admin/personals/update",
      add_affect: "admin/personals/add-affectation",
      delete: "admin/personals/delete",
      change_code: "admin/personals/change_code",
      roles: "admin/roles",
      not_affected: "admin/personals/not_affected",
      get_by_role: "admin/personals/role",
      change_status: "admin/personals/change_status",
      secret_code: "admin/personals/secret_code",
    },
    clients:{
      all: "admin/clients",
      detail: "admin/clients/details",
      add: "admin/clients/add",
      update: "admin/clients/update",
      delete: "admin/clients/delete",
    },
    users:{
      all: "admin/users",
      detail: "admin/users/details",
      add: "admin/users/add",
      update: "admin/users/update",
      change_password: "admin/users/change_password",
      roles: "admin/roles",
      disconnected: "admin/users/disconnected",
    },


    //PRODUCTS
    categories:{
      all: "admin/categories",
      add: "admin/categories/add",
      update: "admin/categories/update",
    } ,
    products:{
      all: "admin/products",
      detail: "admin/products/details",
      alerts: "admin/products/alerts",
      add: "admin/products/add",
      update: "admin/products/update",
      packs: "admin/products/packs",
      clients_subscribe: "admin/products/clients-subscribe",
      cotisations: "admin/products/cotisations",
    },
    packs:{
      all: "admin/packs",
      detail: "admin/packs/details",
      add: "admin/packs/add",
      update: "admin/packs/update",
      provisions: "admin/packs/provisions",
      subscriptions: "admin/packs/subscriptions",
      products: "admin/packs/products",
    },

    accounting:{
      cashiers: "admin/accounting/cashiers",
      versements: "admin/accounting/versements",
      subscriptions: "admin/accounting/subscriptions",
      payments: "admin/accounting/payments",
      cotisations: "admin/accounting/cotisations",
      transactions: "admin/accounting/transactions",
      controls: "admin/accounting/controls",
    }
  },

  common:{
    // countries & cities & quarters
    countries:{
      all: "common/countries",
      detail: "common/countries/details",
      add: "common/countries/add",
      update: "common/countries/update",
    },
    cities:{
      all: "admin/cities",
      detail: "common/cities/details",
      add: "common/cities/add",
      update: "common/cities/update",
    },
    quarters:{
      all: "admin/quarters",
      detail: "common/quarters/details",
      add: "common/quarters/add",
      update: "common/quarters/update",
    }
  }
};
