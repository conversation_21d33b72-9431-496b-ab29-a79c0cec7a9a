<script lang="ts">
import { defineComponent, ref } from 'vue';
import BreadCrumb from 'src/layouts/BreadCrumb.vue';
import { useRoute } from 'vue-router';
import {personalStore} from 'src/stores/core/personalStore';
import { storeToRefs } from 'pinia';
import { useQuasar } from 'quasar';


export default defineComponent({
  name: 'UserDetailPage',
  components: {
    BreadCrumb
  },
  setup() {
    const bread = ref({
      pageTitle: "Utilisateurs",
      subTitle: "Details Utilisateurs"
    });
    const route = useRoute();
    const tab = ref('infos');
    const dialog = ref(false);
    const showLoading = ref(false);
    const $q = useQuasar();

    const id = Number(route.params.userId);
    const store = personalStore();
    const {roles} = storeToRefs(personalStore());
    const {get_userId,updateUser,changeCode,changePassword} = store;
    const user = get_userId(id);

    const form = ref({
      user_id: user?.id,
      username: user?.username,
      email: user?.email,
      phone: user?.phone,
      role_id: user?.role_id,
      status: user?.status,
    });

    const pwdForm = ref({
      user_id: user?.id,
      password: "",
      password_confirmation: "",
      secret_code: ""
    });

    const status = ref([
      { label: "Actif", value: 1},
      { label: "Inactif", value: 0 },
    ]);

    const onUpdateUser = async()=>{
      console.log(form.value);
      showLoading.value = true;
      try {
        console.log('====================================');
        console.log('form submitted',form.value);
        console.log('====================================');
        const res = await updateUser(form.value);
        console.log('=============res=======================');
        console.log("api response",res);
        console.log('====================================');
        if (res.message) {
          showLoading.value = false;
        }
        if (res.success) {
          showLoading.value = false;
          $q.notify({
            color: "green",
            type: 'positive',
            icon: "check",
            position: 'top-right',
            progress: true,
            timeout: 3500,
            message: res.message
          });
          form.value = {
            user_id: "" as any,
            username: "",
            email: "",
            phone: "" as any,
            role_id: "" as any,
            status: "",
          };
          dialog.value = false;
        }else{
          $q.notify({
            color: "red-4",
            type: 'negative',
            icon: "error",
            position: 'top-right',
            progress: true,
            timeout: 3500,
            message: res.message
          });
        }

      } catch (error) {
        console.log("Error : ", error );
        showLoading.value = false;
        $q.notify({
          color: "red-4",
          type: 'negative',
          icon: "error",
          position: 'top-right',
          progress: true,
          timeout: 3500,
          message: "Erreur d'ajout, veuillez réessayer svp"
        });
      }
    }

    const onChangePassword = async()=>{
      console.log(pwdForm.value);
      showLoading.value = true;
      try {
        console.log('====================================');
        console.log('form submitted',pwdForm.value);
        console.log('====================================');
        const res = await changePassword(pwdForm.value);
        console.log('=============res=======================');
        console.log("api response",res);
        console.log('====================================');
        if (res.message) {
          showLoading.value = false;
        }
        if (res.success) {
          showLoading.value = false;
          $q.notify({
            color: "green",
            type: 'positive',
            icon: "check",
            position: 'top-right',
            progress: true,
            timeout: 3500,
            message: res.message
          });
          pwdForm.value = {
            user_id: user?.id,
            password: "",
            password_confirmation: "",
            secret_code: ""
          }
        }else{
          $q.notify({
            color: "red-4",
            type: 'negative',
            icon: "error",
            position: 'top-right',
            progress: true,
            timeout: 3500,
            message: res.message
          });
        }
      } catch (error) {
        console.log("Error : ", error );
        showLoading.value = false;
        $q.notify({
          color: "red-4",
          type: 'negative',
          icon: "error",
          position: 'top-right',
          progress: true,
          timeout: 3500,
          message: "Erreur d'ajout, veuillez réessayer svp"
        });
      }


    }

    return {
      bread,user,tab,form,roles,onUpdateUser,status,showLoading,pwdForm,onChangePassword
    }
  }
})
</script>
<template>
  <q-page  class="q-pa-md">
    <!-- <BreadCrumb :bread="bread" /> -->
    <div class="row q-col-gutter-sm">
      <div class="col col-md-4 col-sm-12 col-xs-12">
        <q-card class="my-card">
          <q-card-section>
            <div style="padding-left: 30%;">
              <q-avatar size="100px" font-size="52px" color="teal" text-color="white" icon="person"  />
            </div>
            <div class="text-center " style="font-weight: bold; font-size: large;">{{ user?.username }}</div>
            <div style="padding-bottom: 50px;" >
              <q-input :model-value="user?.username" type="text" label="Nom" filled readonly />
              <q-input :model-value="user?.role?.name" type="text" label="Role" filled readonly />
              <q-input :model-value="user?.phone" type="text" label="Téléphone" filled readonly/>
              <q-input :model-value="user?.email" type="text" label="Email" filled readonly/>
              <!-- <q-input :model-value="patient?.adress" type="text" label="Adresse" /> -->
            </div>
          </q-card-section>

        </q-card>
      </div>

      <div class="col col-md-8 col-sm-12 col-xs-12">
        <q-card class="my-card">
          <q-card-section>
            <q-tabs  v-model="tab" class="text-teal" align="justify" inline-label >
              <q-tab name="infos" icon="info"  label="Infos"  />
              <q-tab name="password" icon="lock" label="Changer Password" />
              <q-tab name="settings" icon="settings" label="Paramètres" />
            </q-tabs>
          </q-card-section>
          <q-card-section>
            <q-tab-panels v-model="tab" animated>
              <q-tab-panel name="infos">
                <div class="text-h6">Détails de l'utilisateur</div>
                <q-form @submit="onUpdateUser"   >
                  <div class="row q-col-gutter-md">
                    <div class="col col-md-12 col-sm-12 col-xs-12">
                      <q-input filled v-model="form.username" label="Nom d'utilisateur" />
                    </div>
                    <div class="col col-md-6 col-sm-12 col-xs-12">
                      <q-input filled v-model="form.phone" label="Téléphone" mask="########" />
                    </div>
                    <div class="col col-md-6 col-sm-12 col-xs-12">
                      <q-input filled v-model="form.email" label="Email" />
                    </div>
                    <div class="col col-md-6 col-sm-12 col-xs-12">
                      <q-select filled v-model="form.role_id" :options="roles" label="Rôle"
                        lazy-rules aria-placeholder="Rôle" option-value="id" option-label="name"
                        emit-value map-options
                      />
                    </div>
                    <div class="col col-md-6 col-sm-12 col-xs-12">
                      <q-select filled v-model="form.status" :options="status" label="Statut"
                        aria-placeholder="Statut" option-value="value" option-label="label"
                        emit-value map-options
                      />
                    </div>
                  </div>
                  <div class="q-mt-md">
                    <q-btn label="ENREGISTRER" type="submit" color="primary"/>
                  </div>
                </q-form>
              </q-tab-panel>
              <q-tab-panel name="password">
                <div class="text-h6">Modification du Mot de Passe</div>
                <q-form @submit="onChangePassword">
                  <div class="row q-col-gutter-md">
                    <div class="col col-md-12 col-sm-12 col-xs-12">
                      <q-input type="password" filled v-model="pwdForm.password" label="Entrez le nouveau mot de passe" />
                    </div>
                    <div class="col col-md-12 col-sm-12 col-xs-12">
                      <q-input type="password" filled v-model="pwdForm.password_confirmation" label="Confirmation du mot de passe" />
                    </div>
                    <div class="col col-md-12 col-sm-12 col-xs-12">
                      <q-input type="password" filled v-model="pwdForm.secret_code" label="Code secret" />
                    </div>
                  </div>
                  <div class="q-mt-md">
                    <q-btn label="MODIFIER LE MOT DE PASSE" type="submit" color="primary"/>
                  </div>
                </q-form>
              </q-tab-panel>
              <q-tab-panel name="settings">
                <div class="text-h6">Paramètres</div>

              </q-tab-panel>
            </q-tab-panels>
          </q-card-section>
          <q-inner-loading :showing="showLoading">
            <q-spinner-gears size="50px" color="primary" />
          </q-inner-loading>
        </q-card>
      </div>
    </div>
  </q-page>
</template>
