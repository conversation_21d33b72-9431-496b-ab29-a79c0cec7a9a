<script lang="ts">
import { computed, defineComponent, onMounted, PropType, ref, toRefs } from 'vue';
import EditPersonalModal from 'src/components/modals/EditPersonalModal.vue';
import { get_phone_format } from 'src/helpers/utils';
import PersonalStatusModal from '../modals/PersonalStatusModal.vue';
import { personalStore } from 'src/stores/core/personalStore';
import { Personal } from 'src/models';
import { storeToRefs } from 'pinia';
import { adminStore } from 'src/stores/core/adminStore';
import EditUserSecretCodeModal from '../modals/EditUserSecretCodeModal.vue';
import LogoutUserModal from '../modals/LogoutUserModal.vue';

type PaginateData = {
  currentPage: number;
  total: number;
  to: number;
  roleId?: number;
}
export default defineComponent({
  name: "PersonalTableComponent",
  props: {
    roleId: {
      type: Number,
      default: () => 0
    }
  },
  components: {
    EditPersonalModal, PersonalStatusModal,LogoutUserModal, EditUserSecretCodeModal
  },
  setup(props) {

    const filter = ref('');
    const loading = ref(false);

    const { roleId } = toRefs(props);
    const { getPersonalByRole } = personalStore();
    const {roles} = storeToRefs(adminStore());

    const pagination = ref({
      sortBy: 'desc',
      descending: false,
      page: 1,
      rowsPerPage: 15,
      rowsNumber: 20
    });

    const personals = ref<Personal[]>([]);

    const headers = [
      { name: 'nom', label: 'NOM', field: 'nom', sortable: true, align: "left" },
      { name: 'prenoms', label: 'PRENOMS ', field: 'prenoms', sortable: true, align: "left" },
      { name: 'email', label: 'EMAIL', field: 'email', sortable: true, align: "left" },
      { name: 'phone', label: 'CONTACT', field: 'phone', sortable: true, align: "left" },
      { name: 'role', label: 'PROFESSION', field: 'role', sortable: true, align: "left" },
      { name: 'city', label: 'VILLE', field: 'city', sortable: true, align: "left" },
      { name: 'quarter', label: 'QUARTIER', field: 'quarter', sortable: true, align: "left" },
      { name: 'current_agency', label: 'AGENCE', field: 'current_agency', sortable: true, align: "left" },
      { name: 'status', label: 'STATUT', field: 'status', sortable: true, align: "left" },
      { name: 'actions', label: 'Actions', field: 'actions', sortable: false, align: "left" },
    ] as any;

    const findRole = computed(() => {
      return roles.value.find((role: any) => role.id === roleId.value);
    });

    const onRequest = async (props: any) => {
      loading.value = true;
      if (props.pagination) {
        pagination.value.page = props.pagination.page;
        pagination.value.rowsPerPage = props.pagination.rowsPerPage;

        const res = await getPersonalByRole({
          page: pagination.value.page,
          limit: pagination.value.rowsPerPage,
          role_id: roleId.value
        });

        if (res.success) {
          loading.value = false;
          pagination.value.rowsNumber = res.result.total;
          pagination.value.page = res.result.current_page;
          personals.value = res.result.data;
        }
      }
    };

    onMounted(async () => {
      loading.value = true;
      const res = await getPersonalByRole({
        limit: 15,
        page: 1,
        role_id: roleId.value
      });
      if (res.success) {
        loading.value = false;
        pagination.value.rowsNumber = res.result.total;
        pagination.value.page = res.result.current_page;
        personals.value = res.result.data;
      } else {
        loading.value = false;
      }
    });

    return {
      pagination, filter, headers, loading, personals, get_phone_format, onRequest,findRole
    };

  }
});
</script>

<template>
  <div class="q-pa-md">
    <q-table flat bordered :title="findRole?.name" :rows="personals" :columns="headers" row-key="name"
      v-model:pagination="pagination" :filter="filter" table-style="max-width: 100%;"
      table-class="my-sticky-header-table" :loading="loading" @request="onRequest" @update:pagination="onRequest">
      <template v-slot:top-right="props">
        <q-btn flat round dense :icon="props.inFullscreen ? 'fullscreen_exit' : 'fullscreen'"
          @click="props.toggleFullscreen" class="q-ml-md" />
        <q-input filled dense debounce="300" v-model="filter" clearable clear-icon="close"
          placeholder="Rechercher un personnel">
          <template v-slot:append>
            <q-icon name="search" />
          </template>
        </q-input>
      </template>

      <template v-slot:no-data="{ icon, message, }">
        <div class="full-width row flex-center text-accent q-gutter-sm">
          <q-icon size="2em" name="sentiment_dissatisfied" />
          <span>
            Aucune donnée trouvée
          </span>
          <q-icon size="2em" :name="filter ? 'filter_b_and_w' : icon" />
        </div>
      </template>
      <template v-slot:body="props">
        <q-tr :props="props">

          <q-td key="nom" :props="props">
            {{ props.row.nom }}
          </q-td>
          <q-td key="prenoms" :props="props">
            {{ props.row.prenoms }}
          </q-td>
          <q-td key="email" :props="props">
            {{ props.row.email }}
          </q-td>
          <q-td key="phone" :props="props">
            {{ get_phone_format(props.row.phone) }}
          </q-td>
          <q-td key="role" :props="props">
            {{ findRole?.name }}
          </q-td>
          <q-td key="city" :props="props">
            {{ props.row?.city?.name }}
          </q-td>
          <q-td key="quarter" :props="props">
            {{ props.row?.quarter?.name }}
          </q-td>
          <q-td key="current_agency" :props="props">
            {{ props.row?.current_agency?.length > 0 ? props.row?.current_agency[0]?.name : '' }}
          </q-td>
          <q-td key="status" :props="props">
            <q-chip size="sm" class="text-dark" outline :color="props.row.status === '1' ? 'positive' : 'negative'">
              {{ props.row.status === '1' ? "ACTIVE" : "INACTIVE" }}
            </q-chip>
          </q-td>
          <q-td key="actions" :props="props" style="display: flex;">
            <q-btn dense flat color="primary" icon="visibility" class="q-mr-xs" size="xs"
              :to="{ name: 'detail-personal', params: { id: props.row.id } }" />
            <EditPersonalModal :personal="props.row" />
            <PersonalStatusModal :personal="props.row" />
            <EditUserSecretCodeModal :user="props.row" v-if="props.row.role_id !== 2" />
            <LogoutUserModal :user="props.row" v-if="props.row?.user.is_online" />
          </q-td>
        </q-tr>
      </template>
    </q-table>
  </div>
</template>
