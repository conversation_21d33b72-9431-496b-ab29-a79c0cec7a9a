type MenuItem = {
  icon?: string;
  title: string;
  value: string;
  path: string;
}
type Menu = {
  icon: string;
  text: string;
  value: string;
  path: string;
  children: MenuItem[];
}

export const links1: Menu[] = [
  {
    icon: 'dashboard',
    text: 'Tableau de bord',
    value: 'dashboard',
    path: '/dashboard',
    children: []
  },
  {
    icon: 'card_giftcard',
    text: 'Carnets',
    value: 'carnets',
    path: '/dashboard/carnets',
    children: []
  },
  {
    icon: 'groups',
    text: 'Clients',
    value: 'clients',
    path: '/dashboard/clients',
    children: []
  },
  {
    icon: 'account_balance',
    text: "Packs",
    value: 'packs',
    path: '/dashboard/packs',
    children: []
  },

  {
    icon: 'analytics',
    text: 'Etats',
    value: 'analytics',
    path: '/dashboard/analytics',
    children: [
      {
        title: "Etat des clients",
        value: "clients",
        path: "/dashboard/analytics/clients"
      },
      {
        title: "Etat des carnets",
        value: "subscriptions",
        path: "/dashboard/analytics/subscriptions"
      },
      {
        title: "Etat des cotisations",
        value: "cotisations",
        path: "/dashboard/analytics/cotisations"
      },
      {
        title: "Etat des versements",
        value: "versements",
        path: "/dashboard/analytics/versements"
      },
      {
        title: "Etat des collecteurs",
        value: "collectors",
        path: "/dashboard/analytics/collectors"
      }

    ]
  },
];
export const links2: Menu[] = [

  {
    icon: 'person',
    text: 'Agents Collecteurs',
    value: 'collectors',
    path: '/dashboard/personals/collectors',
    children: []
  },
  {
    icon: 'supervisor_account',
    text: 'Agents Superviseurs',
    value: 'supervisors',
    path: '/dashboard/personals/supervisors',
    children: []
  },
  {
    icon: 'account_balance',
    text: 'Agents caissiers',
    value: "cashiers",
    path: "/dashboard/personals/cashiers",
    children: []
  },
  {
    icon: 'local_shipping',
    text: 'Agents Livreurs',
    value: 'livreurs',
    path: '/dashboard/personals/livreurs',
    children: []
  },
  {
    icon: 'business_center',
    text: "Chef d'agence",
    value: "agency_chiefs",
    path: "/dashboard/personals/agency_chiefs",
    children: []
  },
  {
    icon: 'home_work',
    text: "Agence",
    value: "agencies",
    path: "/dashboard/agencies",
    children: []
  },
];

export const links3: Menu[] = [
  {
    icon: 'account_balance_wallet',
    text: 'Etat Caisses',
    value: 'accounting',
    path: '/dashboard/accounting',
    children: []
  },
  {
    icon: 'receipt_long',
    text: 'Versements',
    value: 'deposits',
    path: '/dashboard/deposits',
    children: []
  },
  {
    icon: 'payments',
    text: 'Cotisations',
    value: 'cotisations',
    path: '/dashboard/cotisations',
    children: []
  },
  {
    icon: 'shield',
    text: 'Contrôles Financiers',
    value: 'controls',
    path: '/dashboard/controls',
    children: []
  }

];
export const links4: Menu[] = [
  {
    icon: 'inventory_2',
    text: "Produits",
    value: 'products',
    path: '/dashboard/products',
    children: []
  },
  {
    icon: 'warehouse',
    text: 'Stocks',
    value: 'stocks',
    path: '/dashboard/stocks',
    children: []
  },
  {
    icon: 'local_shipping',
    text: 'Livraisons',
    value: 'deliveries',
    path: '/dashboard/deliveries',
    children: []
  },
];

export const links5: Menu[] = [
  {
    icon: 'home_work',
    text: "Agences",
    value: 'agencies',
    path: '/dashboard/agencies',
    children: []
  },
  {
    icon: 'groups',
    text: 'Utilisateurs',
    value: 'users',
    path: '/dashboard/users',
    children: []
  },
  {
    icon: 'map',
    text: 'Déploiement',
    value: 'localisation',
    path: '/dashboard/localisation/countries',
    children: []
  },

  {
    icon: 'settings',
    text: 'Paramètres',
    value: 'settings',
    path: '/dashboard/settings',
    children: []
  },
  {
    icon: 'help',
    text: 'Support',
    value: 'helps',
    path: '/dashboard/helps',
    children: []
  },
];
