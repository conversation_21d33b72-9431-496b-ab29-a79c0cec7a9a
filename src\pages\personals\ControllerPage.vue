<script lang="ts">
import { defineComponent, onMounted, ref } from 'vue';
import BreadCrumb from 'src/layouts/BreadCrumb.vue';
import PersonalTableComponent from 'src/components/tables/PersonalTableComponent.vue';
import AddPersonalModal from 'src/components/modals/AddPersonalModal.vue';
export default defineComponent({
  name: "ControllerPage",
  components: {
    BreadCrumb, PersonalTableComponent,AddPersonalModal,
  },
  setup() {

    const bread = ref({
      pageTitle: "Collecteurs",
      subTitle: "Gestion des Collecteurs"
    });

    const roleId = ref(4);

    onMounted(async () => {

    });

    return {
      bread, roleId
    };
  }
})
</script>
<template>
  <q-page class="q-pa-md">
    <BreadCrumb :bread="bread" />
    <div class="row q-col-gutter-sm">
      <div class="col col-md-12 col-sm-12">
        <q-card class="my-card" flat  >
          <q-toolbar class="">
            <q-toolbar-title>
              Gestion des agents controlleurs
            </q-toolbar-title>
            <AddPersonalModal :role-id="roleId"  />
          </q-toolbar>
          <PersonalTableComponent :rol-id="roleId" />
        </q-card>
      </div>
    </div>
  </q-page>
</template>
