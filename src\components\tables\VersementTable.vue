<script lang="ts">
import { storeToRefs } from 'pinia';
import { adminStore } from 'src/stores/core/adminStore';
import { defineComponent, onMounted, ref } from 'vue';
import { get_amount_format, get_date_format } from 'src/helpers/utils';

export default defineComponent({
  name: "VersementTable",
  setup() {
    const pagination = ref({
      sortBy: 'desc',
      descending: false,
      page: 1,
      rowsPerPage: 20,
      rowsNumber: 20
    });
    const filter = ref('');
    const loading = ref(false);

    const store = adminStore();
    const { versements } = storeToRefs(adminStore());
    const { getVersements } = store;

    const headers = [
      // { name: 'id', label: 'ID', field: 'id', sortable: true,hide:true },
      { name: 'qrcode', label: 'CODE VERSMENT', field: 'qrcode', sortable: true, align: 'left' },
      { name: 'agency', label: 'AGENCE ', field: 'description', sortable: true, align: 'left' },
      { name: 'cashier', label: 'AGENT CAISSIER', field: 'cashier', sortable: true, align: 'left' },
      { name: 'collector', label: 'AGENT COLLECTEUR', field: 'collector', sortable: true, align: 'left' },
      { name: 'amount', label: 'TOTAL VERSER', field: 'amount', sortable: true, align: 'left' },
      { name: 'amount_remaining', label: 'MONTANT RESTANT', field: 'amount_remaining', sortable: true, align: 'left' },
      { name: 'payment_date', label: 'DATE VERSEMENT', field: 'payment_date', sortable: true, align: 'left' },
      { name: 'confirmed_at', label: 'DATE CONFIRMATION', field: 'confirmed', sortable: true, align: 'left' },
      { name: 'status', label: 'STATUS', field: 'status', sortable: true, align: 'left' },

      // { name: 'actions', label: 'Actions', field: 'actions', sortable: false },
    ] as any;

    const onRequest = async (props: any) => {
      loading.value = true;
      pagination.value.page = props.pagination.page;
      pagination.value.rowsPerPage = props.pagination.rowsPerPage;

      const params = {
        page: pagination.value.page,
        limit: pagination.value.rowsPerPage,
      };

      const res = await getVersements(params);

      if (res.success) {
        loading.value = false;
        pagination.value.rowsNumber = res.result.total;
        pagination.value.page = res.result.current_page;
        versements.value = res.result.data;
      }
    };


    onMounted(async () => {
      loading.value = true;
      const res = await getVersements({
        limit: 20,
        page: 1
      });
      if (res.success) {
        loading.value = false;
        pagination.value.rowsNumber = res.result.total;
        pagination.value.page = res.result.current_page;
        versements.value = res.result.data;
      } else {
        loading.value = false;
      }
    });

    return {
      pagination, filter, headers, versements,loading,
      get_amount_format, get_date_format,onRequest,
    };

  }
});
</script>

<template>
  <div class="q-pa-sm">
    <q-table flat bordered title="Liste des versements" :rows="versements" :columns="headers" row-key="name"
    v-model:pagination="pagination" :filter="filter" table-style="max-width: 100%;" :loading="loading"
    table-class="my-sticky-header-table" @request="onRequest">
      <template v-slot:top-right="props">
        <q-btn flat round dense :icon="props.inFullscreen ? 'fullscreen_exit' : 'fullscreen'"
          @click="props.toggleFullscreen" class="q-ml-md" />
        <q-input filled dense debounce="300" v-model="filter" clearable clear-icon="close"
          placeholder="Rechercher un versement">
          <template v-slot:append>
            <q-icon name="search" />
          </template>
        </q-input>
      </template>

      <template v-slot:no-data="{ icon, message, }">
        <div class="full-width row flex-center text-accent q-gutter-sm">
          <q-icon size="2em" name="sentiment_dissatisfied" />
          <span>
            Aucune donnée disponible trouvée...
          </span>
          <q-icon size="2em" :name="filter ? 'filter_b_and_w' : icon" />
        </div>
      </template>
      <template v-slot:body="props">
        <q-tr :props="props">
          <q-td key="qrcode" :props="props">
            {{ props.row.qrcode }}
          </q-td>
          <q-td key="agency" :props="props">
            {{ props.row.agency?.name }}
          </q-td>
          <q-td key="cashier" :props="props">
            {{ props.row.cashier?.nom }} {{ props.row.cashier?.prenoms }}
          </q-td>
          <q-td key="collector" :props="props">
            {{ props.row.collector?.nom }} {{ props.row.collector?.prenoms }}
          </q-td>
          <q-td key="amount" :props="props">
            {{ get_amount_format(props.row.amount) }}
          </q-td>
          <q-td key="amount_remaining" :props="props">
            {{ get_amount_format(props.row.amount_remaining) }}
          </q-td>
          <q-td key="payment_date" :props="props">
            {{ get_date_format(props.row.payment_date) }}
          </q-td>
          <q-td key="confirmed_at" :props="props">
            {{ get_date_format(props.row.confirmed_at) }}
          </q-td>
          <q-td key="status" :props="props">
            <q-chip :color="props.row.status !== 'confirmed' ? 'warning' : 'positive'" text-color="white">
              {{ props.row.status }}
            </q-chip>
          </q-td>

          <q-td key="actions" :props="props">
            <q-btn dense flat color="primary" icon="visibility" class="q-mr-xs" />
          </q-td>
        </q-tr>
      </template>
    </q-table>
  </div>
</template>
