import { defineStore } from 'pinia'
import { postData, postDataWithToken, getDataWithToken, getDataWithParams } from '../../helpers/http';
import { Client, Personal, Response, Role, User } from 'src/models';
import { api } from 'src/router/api';
// import { roles } from 'src/data/roles';

export const personalStore = defineStore('personal', {
  // other options...
  state: () => ({
    loading: false,
    personals: [] as Personal[],
    personal: {} as Personal,
    clients: [] as Client[],
    client: {} as Client,
    users: [] as User[],
  }),
  persist: true,

  getters: {
    get_personals: (state) => {
      return state.personals;
    },

    get_personal: (state) => {
      return (id: number) => {
        return state.personals.find((personal) => personal.id === id);
      }
    },

    get_personals_byRole: (state) => {
      return (id: number) => {
        return state.personals.filter((personal) => personal.role_id === id);
      }
    },

    get_userId: (state) => {
      return (id: number) => {
        return state.users.find((user) => user.id === id);
      }
    },

    get_client_byCode: (state) =>{
      return (code: string) => {
        return state.clients.find((client) => client.code == code);
      }
    }
  },

  actions: {
    
    async getPersonals(payload: any): Promise<Response> {
      try {
        const response = await getDataWithParams(api.admin.personals.all,payload);
        if (response.success) {
          this.personals = response.result.data;
        }
        return response;
      } catch (error) {
        console.log(error);
        return {
          success: false,
          message: 'Erreur de connexion veuillez réessayer plus tard',
          result: null,
          errors: null
        };
      }
    },

    async getPersonalNotAffected(payload: any): Promise<Response> {
      try {
        const response = await postDataWithToken(api.admin.personals.not_affected, payload);
        return response;
      } catch (error) {
        console.log(error);
        return {
          success: false,
          message: 'Erreur de connexion veuillez réessayer plus tard',
          result: null,
          errors: null
        };
      }
    },

    async getPersonalByRole(payload: any): Promise<Response> {
      try {
        const response = await getDataWithParams(api.admin.personals.get_by_role, payload);
        return response;
      } catch (error) {
        console.log(error);
        return {
          success: false,
          message: 'Erreur de connexion veuillez réessayer plus tard',
          result: null,
          errors: null
        };
      }
    },

    async getClients(payload?: any): Promise<Response> {
      try {
        const response = await getDataWithParams(api.admin.clients.all,payload);
        if (response.success) {
          this.clients = response.result.data;
        }
        return response;
      } catch (error) {
        console.log(error);
        return {
          success: false,
          message: 'Erreur de connexion veuillez réessayer plus tard',
          result: null,
          errors: null
        };
      }
    },

    async getDetailClient(client_id: number): Promise<Response> {
      try {
        const response = await postDataWithToken(api.admin.clients.detail, {
          client_id: client_id
        });
        if (response.success) {
          this.client = response.result;
        }
        return response;
      } catch (error) {
        console.log(error);
        return {
          success: false,
          message: 'Erreur de connexion veuillez réessayer plus tard',
          result: null,
          errors: null
        };
      }
    },

    async updateClient(payload: any): Promise<Response> {
      try {
        const response = await postDataWithToken(api.admin.clients.update, payload);

        return response;
      } catch (error) {
        console.log(error);
        return {
          success: false,
          message: 'Erreur de connexion veuillez réessayer plus tard',
          result: null,
          errors: null
        };
      }
    },

    /**
     *
     * @param personal_id
     * @returns
     */
    async getDetailPersonal(personal_id: number): Promise<Response> {
      try {
        const response = await postDataWithToken(api.admin.personals.detail,{
          personal_id: personal_id
        });
        if (response.success) {
          this.personal = response.result;
        }
        return response;
      } catch (error) {
        console.log(error);
        return {
          success: false,
          message: 'Erreur de connexion veuillez réessayer plus tard',
          result: null,
          errors: null
        };
      }
    },

    async addPersonals(payload: any): Promise<Response> {
      try {
        const response = await postDataWithToken(api.admin.personals.add, payload);
        
        return response;
      } catch (error) {
        console.log(error);
        return {
          success: false,
          message: 'Erreur de connexion veuillez réessayer plus tard',
          result: null,
          errors: null
        };
      }
    },

    async updatePersonal(payload: any): Promise<Response> {
      try {
        const response = await postDataWithToken(api.admin.personals.update, payload);
        
        return response;
      } catch (error) {
        console.log(error);
        return {
          success: false,
          message: 'Erreur de connexion veuillez réessayer plus tard',
          result: null,
          errors: null
        };
      }
    },

    async addMutation(payload: any): Promise<Response> {
      try {
        const response = await postDataWithToken(api.admin.personals.add_affect, payload);
        return response;
      } catch (error) {
        console.log(error);
        return {
          success: false,
          message: 'Erreur de connexion veuillez réessayer plus tard',
          result: null,
          errors: null
        };
      }
    },

    async changeCode(payload: any): Promise<Response> {
      try {
        const response = await postDataWithToken(api.admin.personals.change_code, payload);
        return response;
      } catch (error) {
        console.log(error);
        return {
          success: false,
          message: 'Erreur de connexion veuillez réessayer plus tard',
          result: null,
          errors: null
        };
      }
    },

    async getUsers(payload: any): Promise<Response> {
      try {
        const response = await getDataWithParams(api.admin.users.all,payload);
        if (response.success) {
          this.users = response.result.data;
        }
        return response;
      } catch (error) {
        console.log(error);
        return {
          success: false,
          message: 'Erreur de connexion veuillez réessayer plus tard',
          result: null,
          errors: error
        };
      }
    },

    async addUser(payload: any): Promise<Response> {
      try {
        const response = await postDataWithToken(api.admin.users.add,payload);
        if (response.success) {
          this.users.push(response.result);
        }
        return response;
      } catch (error) {
        console.log(error);
        return {
          success: false,
          message: 'Erreur de connexion veuillez réessayer plus tard',
          result: null,
          errors: error
        };
      }
    },

    async updateUser(payload: any): Promise<Response> {
      try {
        const response = await postDataWithToken(api.admin.users.update,payload);

        return response;
      } catch (error) {
        console.log(error);
        return {
          success: false,
          message: 'Erreur de connexion veuillez réessayer plus tard',
          result: null,
          errors: error
        };
      }
    },

    async changePassword(payload: any): Promise<Response> {
      try {
        const response = await postDataWithToken(api.admin.users.change_password,payload);
        return response;
      } catch (error) {
        console.log(error);
        return {
          success: false,
          message: 'Erreur de connexion veuillez réessayer plus tard',
          result: null,
          errors: error
        };
      }
    },

    async disconnectUser(payload: any): Promise<Response> {
      try {
        const response = await postDataWithToken(api.admin.users.disconnected,payload);
        return response;
      } catch (error) {
        console.log(error);
        return {
          success: false,
          message: 'Erreur de connexion veuillez réessayer plus tard',
          result: null,
          errors: error
        };
      }
    },

    async changeStatus(payload: any): Promise<Response> {
      try {
        const response = await postDataWithToken(api.admin.personals.change_status,payload);
        return response;
      } catch (error) {
        console.log(error);
        return {
          success: false,
          message: 'Erreur de connexion veuillez réessayer plus tard',
          result: null,
          errors: error
        };
      }
    },

    async getPersonalCode(payload: any): Promise<Response> {
      try {
        const response = await postDataWithToken(api.admin.personals.secret_code,payload);
        return response;
      } catch (error) {
        console.log(error);
        return {
          success: false,
          message: 'Erreur de connexion veuillez réessayer plus tard',
          result: null,
          errors: error
        };
      }
    },

    async getDetailUser(user_id: number): Promise<Response> {
      try {
        const response = await postDataWithToken(api.admin.users.add,{
          user_id: user_id
        });
        return response;
      } catch (error) {
        console.log(error);
        return {
          success: false,
          message: 'Erreur de connexion veuillez réessayer plus tard',
          result: null,
          errors: error
        };
      }
    },



  },





});
