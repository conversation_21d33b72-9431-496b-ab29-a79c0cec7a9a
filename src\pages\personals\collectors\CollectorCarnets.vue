<script lang="ts">

  import {defineComponent,onMounted,ref,PropType,toRefs} from 'vue';
  import {get_amount_format,get_date_format} from 'src/helpers/utils';
  import { Subscription } from 'src/models';

  export default defineComponent({
    name: "CollectorCarnets",
    props:{
      subscriptions: {
        type: Array as PropType<Subscription[]>,
        required: true
      }
    },
    setup(props){
      const initialPagination = ref({
        sortBy: 'name',
        descending: true,
        page: 1,
        rowsPerPage: 5
      });
      const filter = ref('');
      const loading = ref(false);

      const {subscriptions} = toRefs(props);

      const headers = [
        { name: 'id', label: 'ID', field: 'id', sortable: true,align:"left" },
        { name: 'code', label: 'CODE CARNET', field: 'code', sortable: true,align:"left" },
        { name: 'agency', label: 'AGENCE ', field: 'description', sortable: true,align:"left" },
        { name: 'client', label: 'CLIENT', field: 'client', sortable: true,align:"left" },
        { name: 'pack', label: 'CARNET(PACK)', field: 'pack', sortable: true,align:"left" },
        { name: 'price', label: 'MONTANT CARNET', field: 'price', sortable: true,align:"left" },
        { name: 'started_at', label: 'DATE DEBUT', field: 'started_at', sortable: true,align:"left" },
        { name: 'finished_at', label: 'DATE FIN', field: 'finished_at', sortable: true,align:"left" },
        { name: 'created_at', label: 'DATE CREATION', field: 'created_at', sortable: true,align:"left" },
        { name: 'status', label: 'STATUS', field: 'status', sortable: true,align:"left" },
        { name: 'actions', label: 'Actions', field: 'actions', sortable: false },
      ] as any;
      const columns_visibles = [
        'code','agency','client','collector','pack','price','started_at',
        'finished_at','status','actions','created_at'
      ];

      onMounted(()=>{
        
        if (subscriptions.value.length > 0) {
          loading.value = false;
          console.log("subscriptions",subscriptions.value);
        }else{
          setTimeout(() => {
            loading.value = false;
          }, 2500);
        }
      });

      return {
        initialPagination, filter, headers,columns_visibles,subscriptions,get_amount_format,get_date_format
      };

    }
  });
</script>

<template>
  <div class="q-pa-md">
    <q-table
      flat bordered
      title="Liste des ventes de carnets"
      :rows="subscriptions"
      :columns="headers"
      row-key="name"
      :pagination="initialPagination"
      :filter="filter"
      table-style="max-width: 100%;"
      :visible-columns="columns_visibles"
    >
      <template v-slot:top-right="props">
        <q-btn
          flat round dense
          :icon="props.inFullscreen ? 'fullscreen_exit' : 'fullscreen'"
          @click="props.toggleFullscreen"
          class="q-ml-md"
        />
        <q-input filled dense debounce="300" v-model="filter" clearable clear-icon="close" placeholder="Rechercher un versement">
          <template v-slot:append>
            <q-icon name="search" />
          </template>
        </q-input>
      </template>

      <template v-slot:no-data="{ icon, message, }">
        <div class="full-width row flex-center text-accent q-gutter-sm">
          <q-icon size="2em" name="sentiment_dissatisfied" />
          <span>
            Aucune catégorie trouvée
          </span>
          <q-icon size="2em" :name="filter ? 'filter_b_and_w' : icon" />
        </div>
      </template>
      <template v-slot:body="props">
        <q-tr :props="props">
          <q-td key="code" :props="props">
            {{ props.row.code }}
          </q-td>
          <q-td key="agency" :props="props">
            {{ props.row.agency?.name }}
          </q-td>
          <q-td key="client" :props="props">
            {{ props.row.client?.nom }} {{ props.row.client?.prenoms }}
          </q-td>
          
          <q-td key="pack" :props="props">
            {{ props.row.pack?.name }} ({{ get_amount_format(props.row.pack?.tarif) }})
          </q-td>
          <q-td key="price" :props="props">
            {{ get_amount_format(props.row.price) }}
          </q-td>
          <q-td key="started_at" :props="props">
            {{ get_date_format(props.row.started_at) }}
          </q-td>
          <q-td key="finished_at" :props="props">
            {{ get_date_format(props.row.finished_at) }}
          </q-td>
          <q-td key="created_at" :props="props">
            {{ get_date_format(props.row.created_at) }}
          </q-td>
          <q-td key="status" :props="props">
            <q-chip
              :color="props.row.status !== 'confirmed' ? 'warning' : 'positive'"
              text-color="white"
            >
            {{ props.row.status }}
          </q-chip>
          </q-td>

          <q-td key="actions" :props="props">
            <q-btn dense flat color="primary" icon="visibility" class="q-mr-xs" />
          </q-td>
        </q-tr>
      </template>
  </q-table>
  </div>
</template>


