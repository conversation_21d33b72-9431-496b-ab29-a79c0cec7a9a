<script lang="ts">
import { storeToRefs } from 'pinia';
import { packStore } from 'src/stores/core/packStore';
import { defineComponent, onMounted, ref } from 'vue';
import { date } from 'quasar';
import { get_date_format, get_amount_format } from 'src/helpers/utils';
import EditPackModal from '../modals/EditPackModal.vue';

export default defineComponent({
  name: "PackTable",
  components: {
    EditPackModal
  },
  setup() {
    const pagination = ref({
      sortBy: 'desc',
      descending: false,
      page: 1,
      rowsPerPage: 20,
      rowsNumber: 20
    });
    const filter = ref('');
    const loading = ref(false);

    const store = packStore();
    const { packs } = storeToRefs(packStore());
    const { getPacks } = store;

    const headers = [
      { name: 'name', label: 'NOM CARNET', field: 'name', sortable: true, align: "left" },
      { name: 'products', label: 'PRODUITS', field: 'products', sortable: true, align: "left" },
      { name: 'clients', label: 'CLIENTS', field: 'clients', sortable: true, align: "left" },
      { name: 'total_price', label: 'MONTANT TOTAL', field: 'total_price', sortable: true, align: "left" },
      { name: 'tarif', label: 'TARIF ', field: 'tarif', sortable: true, align: "left" },
      { name: 'duration', label: 'DUREE COTISATION ', field: 'duration', sortable: true, align: "left" },
      { name: 'status', label: 'STATUT CARNET', field: 'status', sortable: true, align: "left" },
      { name: 'created_at', label: 'DATE CREATION', field: 'created_at', sortable: true, align: "left" },
      { name: 'actions', label: 'Actions', field: 'actions', sortable: false },
    ] as any;

    const onRequest = async (props: any) => {
      loading.value = true;
      pagination.value.page = props.pagination.page;
      pagination.value.rowsPerPage = props.pagination.rowsPerPage;

      const res = await getPacks({
        page: pagination.value.page,
        limit: pagination.value.rowsPerPage,
      });

      if (res.success) {
        loading.value = false;
        pagination.value.rowsNumber = res.result.total;
        pagination.value.page = res.result.current_page;
        packs.value = res.result.data;
      }
    };

    onMounted(async () => {
      loading.value = true;
      const res = await getPacks({
        limit: 20,
        page: 1
      });
      if (res.success) {
        loading.value = false;
        pagination.value.rowsNumber = res.result.total;
        pagination.value.page = res.result.current_page;
        packs.value = res.result.data;
      } else {
        loading.value = false;
      }
    });

    return {
      pagination, filter, headers, loading, packs, get_date_format, get_amount_format,onRequest,
    };

  }
});
</script>

<template>
  <div class="q-pa-xs">
    <q-table flat bordered title="Liste des packs" :rows="packs" :columns="headers" row-key="name"
    v-model:pagination="pagination" :filter="filter" table-style="max-width: 100%;" :loading="loading"
    table-class="my-sticky-header-table" @request="onRequest">
      <template v-slot:top-right="props">
        <q-btn flat round dense :icon="props.inFullscreen ? 'fullscreen_exit' : 'fullscreen'"
          @click="props.toggleFullscreen" class="q-ml-md" />
        <q-input filled dense debounce="300" v-model="filter" clearable clear-icon="close"
          placeholder="Rechercher une catégorie">
          <template v-slot:append>
            <q-icon name="search" />
          </template>
        </q-input>
      </template>

      <template v-slot:no-data="{ icon, message, }">
        <div class="full-width row flex-center text-accent q-gutter-sm">
          <q-icon size="2em" name="sentiment_dissatisfied" />
          <span>
            Aucune catégorie trouvée
          </span>
          <q-icon size="2em" :name="filter ? 'filter_b_and_w' : icon" />
        </div>
      </template>
      <template v-slot:body="props">
        <q-tr :props="props">
          <q-td key="name" :props="props">
            {{ props.row.name }}
          </q-td>

          <q-td key="products" :props="props">
            {{ props.row.products_count }}
          </q-td>
          <q-td key="clients" :props="props">
            {{ props.row.clients_count }}
          </q-td>
          <q-td key="total_price" :props="props">
            {{ get_amount_format(props.row.total_price) }}
          </q-td>
          <q-td key="tarif" :props="props">
            {{ get_amount_format(props.row.tarif) }}
          </q-td>
          <q-td key="duration" :props="props">
            {{ props.row.duration }}
          </q-td>
          <q-td key="status" :props="props">
            <q-chip outline class="" :color="props.row.status == 1 ? 'positive' : 'negative'">
              {{ props.row.status == 1 ? 'Actif' : 'Inactif' }}
            </q-chip>
          </q-td>
          <q-td key="created_at" :props="props">
            {{ get_date_format(props.row.created_at) }}
          </q-td>
          <q-td key="actions" :props="props" >
            <div style="display: flex;">
              <q-btn dense flat color="primary" icon="visibility" class="q-mr-xs" />
              <EditPackModal :pack="props.row" />
            </div>
          </q-td>
        </q-tr>
      </template>
    </q-table>
  </div>
</template>
