<script lang="ts">
import { defineComponent, ref, PropType } from 'vue';
import { useRoute } from 'vue-router';
import type { Menu } from 'src/models';

export default defineComponent({
  name: 'MenuSection',
  props: {
    title: String, // Titre de la section
    links: Array as PropType<Menu[]>,  // Liste des liens de menu
  },
  setup() {
    const route = useRoute();
    return { route };
  },
});
</script>

<template>
  <div>
    <!-- En-tête de la section -->
    <q-item-label v-if="title" header class="text-primary q-mt-sm">
      {{ title }}
    </q-item-label>

    <!-- Liens de menu -->
    <template v-for="link in links" :key="link.text">
      <!-- Élément expansible si le lien a des enfants -->
      <q-expansion-item v-if="link.childreen && link.childreen.length > 0" expand-separator :icon="link.icon"
        :label="link.text" hide-expand-icon>
        <q-item v-for="child in link.childreen" :key="child.title" v-ripple clickable :to="child.path"
          :class="{ 'active-menu': route.path === child.path }">
          <q-item-section avatar>
            <q-icon color="grey-8" :name="child.icon" size="xs" />
          </q-item-section>
          <q-item-section>
            <q-item-label>{{ child.title }}</q-item-label>
          </q-item-section>
        </q-item>
      </q-expansion-item>

      <!-- Lien simple si le lien n'a pas d'enfants -->
      <q-item v-else v-ripple clickable :to="link.path" :class="{ 'active-menu': route.path === link.path }">
        <q-item-section avatar>
          <q-icon color="grey-8" :name="link.icon" size="xs" />
        </q-item-section>
        <q-item-section>
          <q-item-label>{{ link.text }}</q-item-label>
        </q-item-section>
      </q-item>
    </template>
  </div>
</template>



<style scoped>
.active-menu {
  border-left: 6px solid #003366;
  color: #003366 !important;
  border-radius: 6px;
  font-weight: lighter;
  background-color: #decdcd;
}

.active-menu:hover {
  background-color: #f8cccc;
}
</style>
