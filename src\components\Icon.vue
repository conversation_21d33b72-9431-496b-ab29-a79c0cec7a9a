<script lang="ts">
import { computed, defineComponent } from 'vue';
import * as icons from "lucide-vue-next";

export default defineComponent({
  name: "Icon",
  props: {
    name: {
      type: String,
      required: true
    },
    size: Number,
    color: String,
    strokeWidth: Number,
    defaultClass: String
  },
  setup(props) {
    const icon = computed(() => icons[props.name as keyof typeof icons]);
    return {
      icon
    }
  }
})
</script>

<template>
  <component
    :is="icon"
    :size="size"
    :color="color"
    :stroke-width="strokeWidth"
    :class="defaultClass"
  />
</template>
