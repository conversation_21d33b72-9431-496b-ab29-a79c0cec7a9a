<script lang="ts">
import { storeToRefs } from 'pinia';
import { useQuasar } from 'quasar';
import { Product } from 'src/models';
import { productStore } from 'src/stores/core/productStore';
import { defineComponent, onMounted, ref, PropType, toRefs } from 'vue';
export default defineComponent({
  name: "EditProductModal",
  props: {
    product: {
      type: Object as PropType<Product>,
      required: true,
    }
  },
  setup(props) {
    const $q = useQuasar();
    const dialog = ref(false);
    const showLoading = ref(false);
    const disabled = ref(false);

    const { product } = toRefs(props);

    const store = productStore();
    const { categories } = storeToRefs(productStore());
    const { updateProduct, getProducts } = store;

    const margin_types = ref([
      { label: "POURCENTAGE", value: "percent" },
      { label: "MONTANT FIXE", value: "amount" },
    ]);

    const form = ref({
      product_id: 0,
      name: "",
      price_achat: 0,
      price_vente: 0,
      stock_quantity: 0 as any,
      category_id: 0 as any,
      profit_margin_type: "amount",
      profit_margin: 0,
      image: null as unknown as File
    });

    const openDialog = () => {
      if (product.value !== undefined || product.value !== "") {
        dialog.value = true;
        form.value = {
          product_id: product.value.id,
          name: product.value.name,
          price_achat: product.value.price_achat,
          price_vente: product.value.price_vente,
          stock_quantity: product.value.stock_quantity,
          category_id: product.value.category_id,
          profit_margin_type: String(product.value.profit_margin_type),
          profit_margin: Number(product.value.profit_margin),
          image: product.value.image as unknown as File
        }
      }
    };

    const onSubmit = async () => {
      disabled.value = true;
      showLoading.value = true;
      try {
        console.log('====================================');
        console.log('form submitted', form.value);
        console.log('====================================');
        const res = await updateProduct(form.value);

        if (res.message) {
          showLoading.value = false;
          disabled.value = false;
        }
        if (res.success) {
          $q.notify({
            color: "green",
            type: 'positive',
            icon: "check",
            position: 'top-right',
            progress: true,
            timeout: 3500,
            message: res.message
          });


          const new_res = await getProducts({
            limit: 20,
            page: 1
          });
          if (new_res.success) {
            form.value = {
              product_id: 0,
              name: "",
              price_achat: 0,
              price_vente: 0,
              stock_quantity: 0 as any,
              category_id: 0 as any,
              profit_margin_type: "amount",
              profit_margin: 0,
              image: null as unknown as File
            }
            dialog.value = false;
          }
        } else {
          $q.notify({
            color: "red-4",
            type: 'negative',
            icon: "error",
            position: 'top-right',
            progress: true,
            timeout: 3500,
            message: res.message
          });
        }
      } catch (error) {
        console.log("Error : ", error);
        showLoading.value = false;
        disabled.value = false;
        console.log(error);
        $q.notify({
          color: "red-4",
          type: 'negative',
          icon: "error",
          position: 'top-right',
          progress: true,
          timeout: 3500,
          message: "Une erreur est survenue"
        });
      }
    };

    const onReset = () => {

    };

    onMounted(async () => {
      // await getCategories();
    })

    return {
      dialog, form, onSubmit, onReset, openDialog, margin_types,
      categories, showLoading, disabled, product
    };

  }
})

</script>
<template>
  <div class="">
    <q-btn size="md" dense flat label="Modifier" color="primary" class="q-mr-xs" @click="openDialog" />
    <q-dialog v-model="dialog" persistent maximized transition-show="slide-up" transition-hide="slide-down">
      <q-card class="my-card">
        <q-form @submit="onSubmit" @reset="onReset">
          <q-toolbar class="bg-primary text-white">
            <q-toolbar-title>
              Modifier le produit {{ product.name }}
            </q-toolbar-title>
            <q-btn flat round dense icon="close" class="q-mr-xs" @click="dialog = false" />
            <q-btn label="Enregistrer" color="positive" type="submit" :disable="disabled" />
          </q-toolbar>
          <q-card-section>
            <div class="row q-col-gutter-md">
              <div class="col col-md-6 col-sm-12 col-xs-12">
                <q-select v-model="form.category_id" :options="categories" label="Catégorie de produit" hint="Catégorie"
                  lazy-rules aria-placeholder="Catégorie" option-label="name" option-value="id" map-options
                  emit-value />
              </div>
              <div class="col col-md-6 col-sm-12 col-xs-12">
                <q-input v-model="form.name" label="Libellé du produit" hint="Libellé du produit" lazy-rules
                  aria-placeholder="Entrez le libellé du produit"
                  :rules="[(val: any) => !!val || 'Veuillez saisir le libellé du produit']" />
              </div>

              <div class="col col-md-6 col-sm-12 col-xs-12">
                <q-input v-model="form.price_achat" label="Prix d'achat" type="number" hint="Prix d'achat du produit"
                  lazy-rules aria-placeholder="Entrez le prix d'achat de ce produit"
                  :rules="[(val: any) => !!val || 'Veuillez saisir le prix d\'achat']" />
              </div>
              <div class="col col-md-6 col-sm-12 col-xs-12">
                <q-input v-model="form.price_vente" label="Prix de vente" type="number" hint="Prix de vente"
                  placeholder="Entrez le prix " />
              </div>
              <div class="col col-md-2 col-sm-12 col-xs-12">
                <q-select v-model="form.profit_margin_type" :options="margin_types" label="Type de marge bénéficiaire"
                  hint="Sélectionnez le type de marge bénéficiaire" lazy-rules
                  aria-placeholder="Sélectionnez le type de marge bénéficiaire" option-label="label"
                  option-value="value" map-options emit-value />
              </div>
              <div class="col col-md-4 col-sm-12 col-xs-12">
                <q-input v-model="form.profit_margin" label="Marge bénéficiaire" type="number" hint="Marge bénéficiaire"
                  lazy-rules placeholder="Entre la valeur du bénéfice sur ce produit" />
              </div>
              <div class="col col-md-6 col-sm-12 col-xs-12">
                <q-input v-model="form.stock_quantity" label="Quantité en stock" hint="Quantité Stock"
                  placeholder="Entrée la quantité en stock disponible pour ce produit" />
              </div>
              <div class="col col-md-12 col-sm-12 col-xs-12">
                <q-file v-model="form.image" label="Image du produit" hint="Image du produit" lazy-rules clearable>
                  <template v-slot:prepend>
                    <q-icon name="attach_file" />
                  </template>
                </q-file>
              </div>
            </div>
          </q-card-section>
          <q-card-actions align="right">
            <q-btn flat label="Annuler" color="primary" @click="dialog = false" />
            <q-btn label="Enregistrer" color="positive" type="submit" />
          </q-card-actions>
          <q-inner-loading :showing="showLoading">
            <q-spinner-gears size="50px" color="primary" />
          </q-inner-loading>
        </q-form>
      </q-card>
    </q-dialog>
  </div>
</template>
