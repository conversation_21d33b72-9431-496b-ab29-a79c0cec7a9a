<script lang="ts">
import { defineComponent, onMounted, ref } from 'vue';
import { date } from 'quasar';
import Revenu<PERSON>hart from 'components/charts/RevenuChart.vue';
import CotisationChart from 'src/components/charts/CotisationChart.vue';
import AgencyChart from 'components/charts/AgencyChart.vue';
import AgenciesChart from 'components/charts/AgenciesChart.vue';
import ClientRegisterChart from 'src/components/charts/ClientRegisterChart.vue';

import ListVenteCarnet from './ListVenteCarnet.vue';
import ListPayment from './ListPayment.vue';
import ListVersementDay from './ListVersementDay.vue';
import LastClientRegister from './LastClientRegister.vue';
import ListCarnetInactif from './ListCarnetInactif.vue';
import { adminStore } from 'src/stores/core/adminStore';
import { storeToRefs } from 'pinia';
import { Agency, Analytical, Client, Cotisation, Subscription, Versement } from 'src/models';
import { get_amount_format } from 'src/helpers/utils';
export default defineComponent({
  name: "Dashboard",
  components: {
    RevenuChart, AgencyChart, ListVenteCarnet, ListPayment, ListVersementDay, LastClientRegister, AgenciesChart,
    ListCarnetInactif, CotisationChart, ClientRegisterChart,
  },
  setup() {
    const today = date.formatDate(Date.now(), 'YYYY-MM-DD');
    const isLoading = ref(true);

    const store = adminStore();
    // const {analytics} = storeToRefs(adminStore());
    const analytical_date = ref(date.formatDate(Date.now(), 'YYYY-MM-DD'));
    const filterDialog = ref(false);
    const dateRange = ref({
      from: date.formatDate(Date.now(), 'YYYY-MM-DD'),
      to: date.formatDate(new Date(), 'YYYY-MM-DD')
    });
    const { getDashboard } = store;
    const analytics = ref<Analytical>({
      total_clients: 0,
      total_carnets: 0,
      total_subscriptions_finished: 0,
      total_subscriptions_delivered: 0,
      total_subscriptions: 0,
      total_subscriptions_amount: 0,
      total_cotisations_amount: 0,
      total_cotisation_amount_completed: 0,
      total_versements_amount: '0',
      subs_by_day_count: 0,
      total_versements_day: 0,
      cotisations_by_day_amount: 0,
      total_agencies: 0,
      gobal_collect_sum_day: 0
    });

    // Filtres de temps pour les statistiques
    const selectedTimeFilter = ref('today');
    const timeFilters = [
      { label: "Aujourd'hui", value: 'today' },
      { label: 'Cette semaine', value: 'week' },
      { label: 'Ce mois', value: 'month' },
      { label: 'Cette année', value: 'year' }
    ];

    // Données pour le récapitulatif
    const summaryStats = ref([
      {
        title: 'Clients',
        value: 0,
        icon: 'groups',
        color: 'primary',
        bgColor: 'primary-1'
      },
      {
        title: 'Souscriptions',
        value: 0,
        icon: 'card_membership',
        color: 'secondary',
        bgColor: 'secondary-1'
      },

      {
        title: 'Souscription terminé',
        value: 0,
        icon: 'inventory_2',
        color: 'green',
        bgColor: 'warning-1'
      },
      {
        title: 'Carnet livré',
        value: 0,
        icon: 'card_giftcard',
        color: 'positive',
        bgColor: 'positive-1'
      },
    ]);

    // Données pour les statistiques des commandes/transactions
    const orderStats = ref([
      {
        title: 'Ventes de Carnets',
        value: 0,
        icon: 'shopping_cart',
        color: 'info',
        bgColor: 'info-1'
      },
      {
        title: 'Montant Collecté',
        value: '0 FCFA',
        icon: 'account_balance_wallet',
        color: 'positive',
        bgColor: 'positive-1'
      },
      {
        title: 'Montant total des cotisations terminés',
        value: '0 FCFA',
        icon: 'payments',
        color: 'purple',
        bgColor: 'purple-1'
      },
      {
        title: 'Collecte du Jour',
        value: '0 FCFA',
        icon: 'today',
        color: 'orange',
        bgColor: 'orange-1'
      }
    ]);

    onMounted(async () => {
      const res = await getDashboard();
      if (res.success) {
        isLoading.value = false;
        const result = res.result as Analytical;
        analytics.value = result;

        // Mise à jour des données du récapitulatif
        summaryStats.value[0].value = result.total_clients;
        summaryStats.value[1].value = result.total_subscriptions;
        summaryStats.value[2].value = result.total_subscriptions_finished;
        summaryStats.value[3].value = result.total_subscriptions_delivered;

        // Mise à jour des données des statistiques des commandes
        orderStats.value[0].value = result.total_subscriptions_amount;
        orderStats.value[1].value = result.total_cotisations_amount;
        orderStats.value[2].value = result.total_cotisation_amount_completed;
        orderStats.value[3].value = result.total_versements_day;

        console.log("analitycal data", analytics.value);
      } else {
        console.log("error", res);
      }
    })

    return {
      today, analytics, get_amount_format, isLoading, analytical_date, getDashboard, dateRange, filterDialog,
      selectedTimeFilter, timeFilters, summaryStats, orderStats
    }
  }
})
</script>
<template>
  <q-page class="q-pa-md">
    <!-- Section Summary -->
    <q-card class="q-mb-lg q-pa-md" flat>
      <div class="row items-center q-mb-md">
        <div class="col">
          <h5 class="text-h6 q-ma-none text-weight-medium text-grey-8">Récapitulatif</h5>
        </div>
      </div>

      <div class="row q-col-gutter-sm">
        <div v-for="(stat, index) in summaryStats" :key="index" class="col-md-3 col-sm-6 col-xs-12">
          <q-skeleton height="120px" v-if="isLoading" />
          <q-card class="my-card q-pa-md" flat bordered v-else>
            <div class="row items-center q-mb-md">
              <div class="col">
                <div class="text-caption text-grey-6 q-mb-xs">{{ stat.title }}</div>
                <div class="text-h5 text-weight-bold text-grey-8">{{ stat.value }}</div>
              </div>
              <div class="col-auto">
                <q-avatar :color="stat.bgColor" :text-color="stat.color" size="50px">
                  <q-icon :name="stat.icon" size="24px" />
                </q-avatar>
              </div>
            </div>
            <!-- Barre de couleur en bas -->
            <div class="absolute-bottom" :class="`bg-${stat.color}`" style="height: 4px; width: 100%;"></div>
          </q-card>
        </div>
      </div>
    </q-card>

    <!-- Section Order Status -->
    <q-card class="q-mb-lg q-pa-md" flat>
      <div class="row items-center justify-between q-mb-md">
        <div class="col-auto">
          <h5 class="text-h6 q-ma-none text-weight-medium text-grey-8">Statistiques Financières</h5>
        </div>
        <div class="col-auto">
          <!-- <div class="row q-col-gutter-xs">
            <q-btn-toggle v-model="selectedTimeFilter" :options="timeFilters" spread rounded unelevated
              class="custom-toggle" toggle-color="primary" color="grey-3" text-color="grey-8" size="xs" />
          </div> -->
        </div>
      </div>

      <div class="row q-col-gutter-sm">
        <div v-for="(stat, index) in orderStats" :key="index" class="col-md-3 col-sm-6 col-xs-12">
          <q-skeleton height="120px" v-if="isLoading" />
          <q-card class="my-card q-pa-md" flat bordered v-else>
            <div class="row items-center q-mb-md">
              <div class="col">
                <div class="text-caption text-grey-6 q-mb-xs">{{ stat.title }}</div>
                <div class="text-h5 text-weight-bold text-grey-8">{{ get_amount_format(stat.value) }}</div>
              </div>
              <div class="col-auto">
                <q-avatar :color="stat.bgColor" :text-color="stat.color" size="50px">
                  <q-icon :name="stat.icon" size="24px" />
                </q-avatar>
              </div>
            </div>
            <!-- Barre de couleur en bas -->
            <div class="absolute-bottom" :class="`bg-${stat.color}`" style="height: 4px; width: 100%;"></div>
          </q-card>
        </div>
      </div>
    </q-card>

    <!-- Section Graphiques et Analyses -->
    <div class="row q-col-gutter-sm q-pt-md q-pb-md">
      <div class="col col-md-12 col-sm-12 col-xs-12">
        <q-card class="my-card" flat bordered>
          <ListVenteCarnet />
        </q-card>
      </div>

      <div class="col col-md-7 col-sm-12 col-xs-12">
        <q-card class="my-card" flat bordered>
          <q-card-section>
            <div class="text-h6">Etat des cotisations</div>
          </q-card-section>
          <q-card-section>
            <CotisationChart />
          </q-card-section>
        </q-card>
      </div>

      <div class="col col-md-5 col-sm-12 col-xs-12">
        <q-card class="my-card" flat bordered>
          <q-card-section>
            <div class="text-h6"> Etat des inscriptions mensuelles</div>
          </q-card-section>
          <q-card-section>
            <ClientRegisterChart />
          </q-card-section>
        </q-card>
      </div>



      <div class="col col-md-12 col-sm-12 col-xs-12">
        <q-card class="my-card" flat bordered>
          <ListPayment />
        </q-card>
      </div>
    </div>

    <div class="row q-col-gutter-sm q-pt-md q-pb-md">
      <div class="col col-md-12 col-sm-12 col-xs-12">
        <q-card class="my-card" style="height: 100%;" flat bordered>
          <ListVersementDay />
        </q-card>
      </div>

      <div class="col col-md-12 col-sm-12 col-xs-12">
        <q-card class="my-card" flat bordered>
          <ListCarnetInactif />
        </q-card>
      </div>
    </div>

    <div>
      <q-dialog v-model="filterDialog" persistent>
        <q-card style="height: 40%; width: 50%;">
          <q-card-section class="">
            <div class="text-h6">Filtrer les données par date</div>
            <div class="row q-col-gutter-sm q-pa-sm">
              <div class="col-md-6 col-sm-12 col-xs-12">
                <q-input outlined v-model="dateRange.from" label="DU" class="q-ml-md"
                  :rules="[val => !!val || 'Veuillez entrer une date']" lazy-rules placeholder="Entrer une date"
                  autocomplete="off" type="date">

                </q-input>
              </div>
              <div class="col-md-6 col-sm-12 col-xs-12">
                <q-input outlined v-model="dateRange.to" label="AU" class="q-ml-md"
                  :rules="[val => !!val || 'Veuillez entrer une date']" lazy-rules placeholder="Entrer une date"
                  autocomplete="off" type="date">

                </q-input>
              </div>
            </div>
          </q-card-section>
          <q-card-actions align="right">
            <q-btn flat label="FERMER" color="negative" v-close-popup />
            <q-btn flat label="RECHERCHER" color="primary" @click="getDashboard" />
          </q-card-actions>
        </q-card>
      </q-dialog>
    </div>
  </q-page>
</template>

<style scoped>
.custom-toggle {
  border-radius: 8px;
  overflow: hidden;
}

.my-card {
  position: relative;
  transition: all 0.3s ease;
}

.my-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.absolute-bottom {
  position: absolute;
  bottom: 0;
  left: 0;
  border-radius: 0 0 4px 4px;
}
</style>
