<script lang="ts">
import { defineComponent, onMounted, ref } from 'vue';
import { date } from 'quasar';
import Revenu<PERSON>hart from 'components/charts/RevenuChart.vue';
import CotisationChart from 'src/components/charts/CotisationChart.vue';
import AgencyChart from 'components/charts/AgencyChart.vue';
import AgenciesChart from 'components/charts/AgenciesChart.vue';
import ClientRegisterChart from 'src/components/charts/ClientRegisterChart.vue';

import ListVenteCarnet from './ListVenteCarnet.vue';
import ListPayment from './ListPayment.vue';
import ListVersementDay from './ListVersementDay.vue';
import LastClientRegister from './LastClientRegister.vue';
import ListCarnetInactif from './ListCarnetInactif.vue';
import { adminStore } from 'src/stores/core/adminStore';
import { storeToRefs } from 'pinia';
import { Agency, Analytical, Client, Cotisation, Subscription, Versement } from 'src/models';
import { get_amount_format } from 'src/helpers/utils';
export default defineComponent({
  name: "Dashboard",
  components: {
    RevenuChart, AgencyChart, ListVenteCarnet, ListPayment, ListVersementDay, LastClientRegister, AgenciesChart,
    ListCarnetInactif, CotisationChart, ClientRegisterChart,
  },
  setup() {
    const today = date.formatDate(Date.now(), 'YYYY-MM-DD');
    const isLoading = ref(true);

    const store = adminStore();
    // const {analytics} = storeToRefs(adminStore());
    const analytical_date = ref(date.formatDate(Date.now(), 'YYYY-MM-DD'));
    const filterDialog = ref(false);
    const dateRange = ref({
      from: date.formatDate(Date.now(), 'YYYY-MM-DD'),
      to: date.formatDate(new Date(), 'YYYY-MM-DD')
    });
    const { getDashboard } = store;
    const analytics = ref<Analytical>({
      total_clients: 0,
      total_carnets: 0,
      total_products: 0,
      total_subscriptions: 0,
      total_subscriptions_amount: 0,
      total_cotisations_amount: 0,
      total_versements_amount: '0',
      subs_by_day_count: 0,
      total_versements_day: 0,
      cotisations_by_day_amount: 0,
      total_agencies: 0,
      gobal_collect_sum_day: 0
    });

    onMounted(async () => {
      const res = await getDashboard();
      if (res.success) {
        isLoading.value = false;
        const result = res.result as Analytical;
        analytics.value = result;
        console.log("analitycal data", analytics.value);
      } else {
        console.log("error", res);
      }
    })

    return {
      today, analytics, get_amount_format, isLoading, analytical_date, getDashboard, dateRange, filterDialog
    }
  }
})
</script>
<template>
  <q-page class="q-pa-md">
    <div class="row q-col-gutter-sm">
      <div class="col col-md-12 col-xs-12">
        <q-card class="my-card " flat style="max-height: 140px; height: 80%;">
          <q-card-section class="flex ">
            <div class="text-h6">
              <q-avatar size="50px" font-size="52px" color="teal" text-color="white" icon="account_circle" />
              BIENVENUE ADMIN
            </div>
            <q-space />
            <div>
              <q-btn flat color="primary" icon="search" @click="filterDialog = true" class="q-ml-md">
                <q-tooltip>
                  Effectuer une recherche sur les ventes de carnets, sur les paiements, les cotisations, etc.
                </q-tooltip>
              </q-btn>
            </div>
          </q-card-section>
        </q-card>
      </div>
      <div class="col col-md-3 col-xs-6">
        <q-skeleton height="160px" square v-if="isLoading" />
        <q-card class="my-card bg-primary text-white" v-else flat bordered>
          <q-card-section>
            <div class="text-h5 "><q-icon color="white" name="groups" /> Clients</div>
          </q-card-section>
          <q-card-section>
            <div class=" text-h5">
              {{ analytics.total_clients }}
            </div>
          </q-card-section>
        </q-card>
      </div>
      <div class="col col-md-3 col-xs-6">
        <q-skeleton height="180px" square v-if="isLoading" />
        <q-card class="my-card bg-primary text-white" v-else flat bordered>
          <q-card-section>
            <div class="text-h5"><q-icon color="white" name="groups" /> Ventes de carnets</div>
          </q-card-section>
          <q-card-section>
            <div class=" text-h5">
              {{ analytics.total_subscriptions }}
            </div>
          </q-card-section>
        </q-card>
      </div>
      <div class="col col-md-3 col-xs-6">
        <q-skeleton height="180px" square v-if="isLoading" />
        <q-card class="my-card bg-primary text-white" v-else flat bordered>
          <q-card-section>
            <div class="text-h6 "><q-icon color="white" name="groups" /> Montant globale collecté </div>
          </q-card-section>
          <q-card-section>
            <div class=" text-h5">
              {{ get_amount_format(analytics.total_cotisations_amount) }}
            </div>
          </q-card-section>
        </q-card>
      </div>
      <div class="col col-md-3 col-xs-6">
        <q-skeleton height="180px" square v-if="isLoading" />
        <q-card class="my-card bg-primary text-white" v-else flat bordered>
          <q-card-section>
            <div class="text-h6 "><q-icon color="white" name="groups" /> Montant global versé</div>
          </q-card-section>
          <q-card-section>
            <div class=" text-h5">
              {{ get_amount_format(analytics.total_versements_amount) }}
            </div>
          </q-card-section>
        </q-card>
      </div>

    </div>

    <div class="row q-col-gutter-sm q-pt-sm q-pb-sm">

      <div class="col col-md-4 col-sm-6 col-xs-6">
        <q-skeleton height="180px" square v-if="isLoading" />
        <q-card class="my-card" v-else flat bordered>
          <q-card-section>
            <div class="text-h6 ">Total des Ventes de Carnets</div>
            <div class="text-subtitle2">
              Montant total des ventes de carnets effectuées
            </div>
          </q-card-section>
          <q-card-section>
            <div class="d-flex">
              <div class=" text-h5">
                {{ get_amount_format(analytics.total_subscriptions_amount) }}
              </div>
            </div>
          </q-card-section>
        </q-card>
      </div>
      <div class="col col-md-4 col-sm-6 col-xs-6">
        <q-skeleton height="180px" square v-if="isLoading" />
        <q-card class="my-card" v-else flat bordered>
          <q-card-section>
            <div class="text-h6 ">Montant Total Cotisé</div>
            <div class="text-subtitle2">
              Montant total des cotisations effectuées
            </div>
          </q-card-section>
          <q-card-section>
            <div class="d-flex">
              <div class=" text-h5">
                {{ get_amount_format(analytics.total_cotisations_amount) }}
              </div>
            </div>
          </q-card-section>
        </q-card>
      </div>
      <div class="col col-md-4 col-sm-12 col-xs-12">
        <q-skeleton height="180px" square v-if="isLoading" />
        <q-card class="my-card" v-else flat bordered>
          <q-card-section>
            <div class="text-h6 "> Somme Totale Reçue Aujourd'hui </div>
            <div class="text-subtitle2">
              Montant total versés aujourd'hui ({{ today }})
            </div>
          </q-card-section>
          <q-card-section>
            <div class="d-flex">
              <div class=" text-h5">
                {{ get_amount_format(analytics.total_versements_day) }}
              </div>
            </div>
          </q-card-section>
        </q-card>
      </div>
    </div>

    <div class="row q-col-gutter-sm q-pt-md q-pb-md">
      <div class="col col-md-7 col-sm-12 col-xs-12">
        <q-card class="my-card" flat bordered>
          <q-card-section>
            <div class="text-h6">Etat des cotisations</div>
          </q-card-section>
          <q-card-section>
            <CotisationChart />
          </q-card-section>
        </q-card>
      </div>

      <div class="col col-md-5 col-sm-12 col-xs-12">
        <q-card class="my-card" flat bordered>
          <q-card-section>
            <div class="text-h6"> Etat des inscriptions mensuelles</div>
          </q-card-section>
          <q-card-section>
            <ClientRegisterChart />
          </q-card-section>
        </q-card>
      </div>

      <div class="col col-md-12 col-sm-12 col-xs-12">
        <q-card class="my-card" flat bordered>
          <ListVenteCarnet />
        </q-card>
      </div>

      <div class="col col-md-12 col-sm-12 col-xs-12">
        <q-card class="my-card" flat bordered>
          <ListPayment />
        </q-card>
      </div>
    </div>

    <div class="row q-col-gutter-sm q-pt-md q-pb-md">
      <div class="col col-md-12 col-sm-12 col-xs-12">
        <q-card class="my-card" style="height: 100%;" flat bordered>
          <ListVersementDay />
        </q-card>
      </div>

      <div class="col col-md-12 col-sm-12 col-xs-12">
        <q-card class="my-card" flat bordered>
          <ListCarnetInactif />
        </q-card>
      </div>
    </div>

    <div>
      <q-dialog v-model="filterDialog" persistent>
        <q-card style="height: 40%; width: 50%;">
          <q-card-section class="">
            <div class="text-h6">Filtrer les données par date</div>
            <div class="row q-col-gutter-sm q-pa-sm">
              <div class="col-md-6 col-sm-12 col-xs-12">
                <q-input outlined v-model="dateRange.from" label="DU" class="q-ml-md"
                  :rules="[val => !!val || 'Veuillez entrer une date']" lazy-rules placeholder="Entrer une date"
                  autocomplete="off" type="date">

                </q-input>
              </div>
              <div class="col-md-6 col-sm-12 col-xs-12">
                <q-input outlined v-model="dateRange.to" label="AU" class="q-ml-md"
                  :rules="[val => !!val || 'Veuillez entrer une date']" lazy-rules placeholder="Entrer une date"
                  autocomplete="off" type="date">

                </q-input>
              </div>
            </div>
          </q-card-section>
          <q-card-actions align="right">
            <q-btn flat label="FERMER" color="negative" v-close-popup />
            <q-btn flat label="RECHERCHER" color="primary" @click="getDashboard" />
          </q-card-actions>
        </q-card>
      </q-dialog>
    </div>
  </q-page>
</template>
