<script lang="ts">
import { computed, defineComponent, onMounted, ref } from 'vue';
import { personalStore } from 'src/stores/core/personalStore';
import { storeToRefs } from 'pinia';
import EditPersonalModal from 'src/components/modals/EditPersonalModal.vue';
import { get_date_format, get_phone_format } from 'src/helpers/utils';
import PersonalStatusModal from '../modals/PersonalStatusModal.vue';
import { adminStore } from 'src/stores/core/adminStore';

export default defineComponent({
  name: "PersonalTable",
  components: { EditPersonalModal, PersonalStatusModal },
  setup() {
    const initialPagination = ref({
      sortBy: 'name',
      descending: false,
      page: 1,
      rowsPerPage: 10
    });
    const filter = ref('');
    const loading = ref(false);

    const pagination = ref({
      sortBy: 'desc',
      descending: false,
      page: 1,
      rowsPerPage: 10,
      rowsNumber: 20
    });

    const store = personalStore();
    const { roles } = storeToRefs(adminStore());
    const { personals } = storeToRefs(personalStore());
    const { getPersonals, getPersonalByRole } = store;


    const headers = [
      // { name: 'id', label: 'ID', field: 'id', sortable: true, align: "left" },
      { name: 'nom', label: 'NOM', field: 'nom', sortable: true, align: "left" },
      { name: 'prenoms', label: 'PRENOMS ', field: 'prenoms', sortable: true, align: "left" },
      { name: 'email', label: 'EMAIL', field: 'email', sortable: true, align: "left" },
      { name: 'phone', label: 'CONTACT', field: 'phone', sortable: true, align: "left" },
      { name: 'role', label: 'PROFESSION', field: 'role', sortable: true, align: "left" },
      { name: 'city', label: 'VILLE', field: 'city', sortable: true, align: "left" },
      { name: 'quarter', label: 'QUARTIER', field: 'quarter', sortable: true, align: "left" },
      { name: 'current_agency', label: 'AGENCE', field: 'current_agency', sortable: true, align: "left" },
      { name: 'status', label: 'STATUT', field: 'status', sortable: true, align: "left" },
      { name: 'actions', label: 'Actions', field: 'actions', sortable: false, align: "left" },
    ] as any;
    const columns_visibles = ['nom', 'prenoms', 'email', 'phone', 'role', 'city', 'quarter', 'current_agency', 'actions', 'status'];

    const personal_roles = computed(() => {
      return roles.value.filter((role) => {
        return role.id !== 1 && role.id !== 2 && role.id !== 8 && role.id !== 9
      });
    });

    const role = ref({
      id: "",
      name: ""
    }) as any;

    const get_personals_by_role = async () => {
      if (role.value) {
        const payload = {
          role_id: role.value.id,
          page: pagination.value.page,
          limit: 15,
        };
        const res = await getPersonalByRole(payload);
        console.log("personals by role response",res);
        
        if (res.success) {
          personals.value = res.result.data;
          pagination.value.page = res.result.current_page;
          pagination.value.rowsNumber = res.result.total;
        }
      }else{
        personals.value = personals.value;
      }
      return personals.value;
    };

    const onRequest = async (props: any) => {
      loading.value = true;
      // console.log("pagination props",props.pagination);
      pagination.value.page = props.pagination.page;
      pagination.value.rowsPerPage = props.pagination.rowsPerPage;

      if (role.value) {
        let payload = {
          role_id: role.value.id,
          page: pagination.value.page,
          limit: pagination.value.rowsPerPage,
        };
        const res = await getPersonalByRole(payload);
        console.log("personals by role response",res);
        
        if (res.success) {
          loading.value = false;
          pagination.value.rowsNumber = res.result.total;
          pagination.value.page = res.result.current_page;
          personals.value = res.result.data;
        }
      }

      const res = await getPersonals({
        page: pagination.value.page,
        limit: pagination.value.rowsPerPage,
      });

      if (res.success) {
        loading.value = false;
        pagination.value.rowsNumber = res.result.total;
        pagination.value.page = res.result.current_page;
        personals.value = res.result.data;
      }
    };

    onMounted(async () => {
      loading.value = true;
      const res = await getPersonals({
        limit: 15,
        page: 1
      });
      if (res.success) {
        loading.value = false;
        pagination.value.rowsNumber = res.result.total;
        pagination.value.page = res.result.current_page;
        personals.value = res.result.data;
      }else{
        loading.value = false;
      }
    });

    return {
      initialPagination, filter, headers, columns_visibles, loading, personals, pagination, personal_roles, role,
      get_date_format, get_phone_format, onRequest, getPersonalByRole, get_personals_by_role
    };

  }
});
</script>

<template>
  <div class="q-pa-xs">
    <q-table flat bordered title="Liste du personels" :rows="personals" :columns="headers" row-key="name"
      v-model:pagination="pagination" :filter="filter" table-style="max-width: 100%;" :loading="loading"
      table-class="my-sticky-header-table" @request="onRequest">
      <template v-slot:top-right="props">
        <q-btn flat round dense :icon="props.inFullscreen ? 'fullscreen_exit' : 'fullscreen'"
          @click="props.toggleFullscreen" class="q-ml-md" />
        <q-select v-model="role" label="Rôle" dense :options="personal_roles" emit-value map-options option-label="name"
          options-dense class="q-ml-md" style="width: 150px" @update:model-value="get_personals_by_role" clearable />
        <q-input filled dense debounce="300" v-model="filter" clearable clear-icon="close"
          placeholder="Rechercher un personnel">
          <template v-slot:append>
            <q-icon name="search" />
          </template>
        </q-input>
      </template>

      <template v-slot:no-data="{ icon, message, }">
        <div class="full-width row flex-center text-accent q-gutter-sm">
          <q-icon size="2em" name="sentiment_dissatisfied" />
          <span>
            Aucune donnée trouvée
          </span>
          <q-icon size="2em" :name="filter ? 'filter_b_and_w' : icon" />
        </div>
      </template>
      <template v-slot:body="props">
        <q-tr :props="props">

          <q-td key="nom" :props="props">
            {{ props.row.nom }}
          </q-td>
          <q-td key="prenoms" :props="props">
            {{ props.row.prenoms }}
          </q-td>
          <q-td key="email" :props="props">
            {{ props.row.email }}
          </q-td>
          <q-td key="phone" :props="props">
            {{ get_phone_format(props.row.phone) }}
          </q-td>
          <q-td key="role" :props="props">
            {{ props.row?.role?.name }}
          </q-td>
          <q-td key="city" :props="props">
            {{ props.row?.city?.name }}
          </q-td>
          <q-td key="quarter" :props="props">
            {{ props.row?.quarter?.name }}
          </q-td>
          <q-td key="current_agency" :props="props">
            {{ props.row?.current_agency?.length > 0 ? props.row?.current_agency[0]?.name : '' }}
          </q-td>
          <q-td key="status" :props="props">
            <q-chip size="sm" class="text-dark" outline :color="props.row.status === '1' ? 'positive' : 'negative'">
              {{ props.row.status === '1' ? "ACTIVE" : "INACTIVE" }}
            </q-chip>
          </q-td>
          <q-td key="actions" :props="props" style="display: flex;">
            <q-btn dense flat color="primary" icon="visibility" class="q-mr-xs" size="xs"
              :to="{ name: 'detail-personal', params: { id: props.row.id } }" />
            <EditPersonalModal :personal="props.row" />
            <PersonalStatusModal :personal="props.row" />
          </q-td>
        </q-tr>
      </template>
    </q-table>
  </div>
</template>
