<script lang="ts">
import { defineComponent, onMounted, ref } from 'vue';
import { personalStore } from 'src/stores/core/personalStore';
import { storeToRefs } from 'pinia';
import EditUserModal from '../modals/EditUserModal.vue';
import { useRouter } from 'vue-router';
import LogoutUserModal from '../modals/LogoutUserModal.vue';
import { get_phone_format } from 'src/helpers/utils';
import EditUserSecretCodeModal from '../modals/EditUserSecretCodeModal.vue';

export default defineComponent({
  name: "UsersTable",
  components: {
    EditUserModal, LogoutUserModal, EditUserSecretCodeModal,
  },
  setup() {
    const router = useRouter();

    const pagination = ref({
      sortBy: 'desc',
      descending: false,
      page: 1,
      rowsPerPage: 10,
      rowsNumber: 20
    });

    const loading = ref(false);
    const filter = ref('');

    const headers = [
      { name: 'username', label: 'Nom Utilisateur', field: 'username', sortable: true, align: "left" },
      { name: 'email', label: 'Email', field: 'email', sortable: true, align: "left" },
      { name: 'phone', label: 'Contact', field: 'phone', sortable: false, align: "left" },
      { name: 'role', label: 'Role', field: 'role', sortable: true, align: "left" },
      { name: 'status', label: 'Status', field: 'status', sortable: false, align: "left" },
      { name: 'is_online', label: 'Connecté ?', field: 'clients', sortable: true, align: "left" },
      { name: 'actions', label: 'Actions', field: 'actions', sortable: false, align: "left" },
    ] as any;

    const store = personalStore();
    const { users } = storeToRefs(personalStore());
    const { getUsers } = store;

    const onRequest = async (props: any) => {
      loading.value = true;
      // console.log("pagination props",props.pagination);
      pagination.value.page = props.pagination.page;
      pagination.value.rowsPerPage = props.pagination.rowsPerPage;

      const res = await getUsers({
        page: pagination.value.page,
        limit: pagination.value.rowsPerPage,
      });

      if (res.success) {
        loading.value = false;
        pagination.value.rowsNumber = res.result.total;
        pagination.value.page = res.result.current_page;
        users.value = res.result.data;
      }
    };


    const go_detail = (userId: number) => {
      console.log(userId);
      router.push(`/dashboard/users/${userId}`);
    }

    onMounted(async () => {
      loading.value = true;
      const res = await getUsers({
        limit: 15,
        page: 1
      });
      if (res.success) {
        loading.value = false;
        pagination.value.rowsNumber = res.result.total;
        pagination.value.page = res.result.current_page;
        users.value = res.result.data;
      }

    });

    return {
      filter, headers, users, loading, pagination, onRequest,
      getUsers, go_detail, get_phone_format
    };

  }
});
</script>

<template>
  <div class="q-pa-md">
    <q-table flat  title="Liste des utilisateurs" :rows="users" :columns="headers" row-key="name"
      v-model:pagination="pagination" :filter="filter" table-style="max-width: 100%;" :loading="loading"
      @request="onRequest">
      <template v-slot:top-right="props">
        <q-btn flat round dense :icon="props.inFullscreen ? 'fullscreen_exit' : 'fullscreen'"
          @click="props.toggleFullscreen" class="q-ml-md" />
        <q-input filled dense debounce="300" v-model="filter" clearable clear-icon="close"
          placeholder="Rechercher un utilisateur">
          <template v-slot:append>
            <q-icon name="search" />
          </template>
        </q-input>
      </template>

      <template v-slot:no-data="{ icon, message, }">
        <div class="full-width row flex-center text-accent q-gutter-sm">
          <q-icon size="2em" name="sentiment_dissatisfied" />
          <span>
            Aucune information trouvée
          </span>
          <q-icon size="2em" :name="filter ? 'filter_b_and_w' : icon" />
        </div>
      </template>
      <template v-slot:body="props">
        <q-tr :props="props">
          <!-- <q-td key="id" :props="props">
            {{ props.row.id }}
          </q-td> -->
          <q-td key="username" :props="props">
            {{ props.row.username }}
          </q-td>
          <q-td key="email" :props="props">
            {{ props.row.email }}
          </q-td>
          <q-td key="phone" :props="props">
            {{ get_phone_format(props.row.phone) }}
          </q-td>
          <q-td key="role" :props="props">
            {{ props.row.role?.name }}
          </q-td>
          <q-td key="status" :props="props">
            <q-chip outline flat :text-color="props.row.status === 1 ? 'positive' : 'negative'">
              {{ props.row.status === 1 ? "ACTIVE" : "INACTIVE" }}
            </q-chip>
          </q-td>
          <q-td key="is_online" :props="props">
            <q-chip outline flat :text-color="props.row.is_online === 1 ? 'positive' : 'negative'">
              {{ props.row.is_online === 1 ? "Oui" : "Non" }}
            </q-chip>
          </q-td>

          <q-td key="actions" :props="props" style="display: flex;">
            <q-btn dense flat color="primary" icon="visibility" class="q-mr-xs" @click="go_detail(props.row.id)" />
            <EditUserModal :user="props.row" />
            <EditUserSecretCodeModal :user="props.row" v-if="props.row.role_id !== 2" />
            <LogoutUserModal :user="props.row" v-if="props.row.is_online" />
          </q-td>
        </q-tr>
      </template>
    </q-table>
  </div>
</template>
