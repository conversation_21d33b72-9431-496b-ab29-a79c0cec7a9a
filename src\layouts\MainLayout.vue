<script lang="ts">
import { ref, onMounted } from 'vue'
import { fabYoutube } from '@quasar/extras/fontawesome-v6';
import { links1, links2, links3, links4,links5 } from './menus';
import { useRouter } from 'vue-router';
import { QSpinnerFacebook, useQuasar } from 'quasar';
import { checkToken, getToken } from 'src/helpers/myfunc';
import { authStore } from 'src/stores/auth/authStore';
import { adminStore } from 'src/stores/core/adminStore';
import { storeToRefs } from 'pinia';
import { computed } from 'vue';
import { productStore } from 'src/stores/core/productStore';
import logo from 'src/assets/logo.jpg';

export default {
  name: 'MyLayout',

  setup() {
    const $q = useQuasar();
    const leftDrawerOpen = ref(false)
    const search = ref('');
    const router = useRouter();
    const isLoading = ref(false);
    const { logout } = authStore();

    const thumbStyle = {
      right: '4px',
      borderRadius: '5px',
      backgroundColor: '#027be3',
      width: '5px',
      opacity: '0.75'
    }

    const barStyle = {
      right: '2px',
      borderRadius: '9px',
      backgroundColor: '#027be3',
      width: '9px',
      opacity: '0.2'
    }

    const store = adminStore();
    const { countries, cities, quarters, roles, agencies } = storeToRefs(adminStore());
    const { categories } = storeToRefs(productStore());
    const prodStore = productStore();
    const { getCountries, getCities, getQuarters, getRoles, getAgencies } = store;

    const isDataLoaded = computed(() =>
      countries.value.length > 0 &&
      cities.value.length > 0 &&
      quarters.value.length > 0 &&
      roles.value.length > 0 &&
      agencies.value.length > 0 &&
      categories.value.length > 0
    );


    function toggleLeftDrawer() {
      leftDrawerOpen.value = !leftDrawerOpen.value
    }

    const showLoading = () => {
      if (isLoading.value) {
        $q.loading.show({
          spinner: QSpinnerFacebook,
          spinnerColor: 'yellow',

          spinnerSize: 140,
          backgroundColor: 'cyan-2',
          message: 'Chargement des données en cours, patientez quelques instants svp ...!',
          messageColor: 'black'
        });
      }
    };

    const hideLoading = () => {
      setTimeout(() => {
        $q.loading.hide();
      }, 3000);
    };

    const onLogout = async () => {
      try {
        console.log("user on disconnect");
        const res = await logout();
        if (res.success) {
          $q.notify({
            type: "positive",
            position: "top-right",
            message: res.message,
            timeout: 2500,
          });
          setTimeout(() => {
            router.push("/");
          }, 2500);
        } else {
          $q.notify({
            type: "negative",
            position: "top-right",
            message: res.message,
            timeout: 2500,
          });
        }
      } catch (error) {
        $q.notify({
          type: "positive",
          position: "top-right",
          message: "Echec, une erreur interne est survenue",
          timeout: 2500,
        });
      }
    };

    const initData = async () => {
      if (!isDataLoaded.value) {  // Ne chargez que si les données sont vides
        isLoading.value = true;
        showLoading();
        try {
          await Promise.all([
            getCountries(),
            getCities(),
            getQuarters(),
            getRoles(),
            getAgencies({ page: 1, limit: 50 }),
            prodStore.getCategories()
          ]);
        } catch (error) {
          console.error("Error initializing data:", error);
        } finally {
          isLoading.value = false;
          hideLoading();
        }
      } else {
        console.log("Data is already loaded in store");
      }
    };


    onMounted(async () => {
      window.document.title = "Dashboard";

      const is_auth = checkToken();
      if (!is_auth) {
        router.push("/");
      } else {
        console.log("init data");
        await initData();
      }
      setInterval(() => {
        const token = getToken();
        if (token == null) {
          router.push("/");
        }
      }, 1000);
    });

    return {
      fabYoutube, leftDrawerOpen, search, links1, links2, links3, links4,links5, logo,
      toggleLeftDrawer, onLogout, thumbStyle, barStyle
    };
  }
}
</script>

<template>
  <q-layout view="lHr LpR fFf" class="bg-grey-1">
    <q-header class="bg-white text-grey-8 q-py-xs" height-hint="58">
      <q-toolbar>
        <q-btn flat dense round @click="toggleLeftDrawer" aria-label="Menu" icon="menu" />

        <q-btn flat no-caps no-wrap class="q-ml-xs" v-if="$q.screen.gt.xs">
          <!-- <q-icon name="credit_card" color="red" size="28px" /> -->
          <q-toolbar-title shrink class="text-cyan-4">
            Dashboard
          </q-toolbar-title>
        </q-btn>

        <q-space />

        <div class="q-gutter-sm row items-center no-wrap">

          <q-btn round dense flat color="grey-8" icon="apps" v-if="$q.screen.gt.sm">
            <q-tooltip>Apps</q-tooltip>
          </q-btn>

          <q-btn round dense flat color="grey-8" icon="notifications">
            <q-badge color="red" text-color="white" floating>
              0
            </q-badge>
            <q-tooltip>Notifications</q-tooltip>
          </q-btn>
          <q-btn-dropdown color="primary" flat>
            <template v-slot:label>
              <q-avatar size="30px">
                <q-icon name="account_circle" />
              </q-avatar>
            </template>
            <div class="row no-wrap q-pa-md">
              <div class="column">
                <div class="text-h6 q-mb-md">Settings</div>
                <q-list>
              <q-item clickable v-close-popup v-ripple>
                <q-item-section>
                  <q-item-label>Profile</q-item-label>
                </q-item-section>
              </q-item>
              <q-item clickable v-close-popup v-ripple>
                <q-item-section>
                  <q-item-label>Paramètre</q-item-label>
                </q-item-section>
              </q-item>

            </q-list>
              </div>

              <q-separator vertical inset class="q-mx-lg" />

              <div class="column items-center">
                <q-avatar size="72px">
                  <img src="https://cdn.quasar.dev/img/boy-avatar.png">
                </q-avatar>

                <div class="text-subtitle1 q-mt-md q-mb-xs">John Doe</div>

                <q-btn color="primary" label="Deconnexion" push size="sm" v-close-popup  @click="onLogout" />
              </div>
            </div>

          </q-btn-dropdown>
        </div>
      </q-toolbar>
    </q-header>

    <q-drawer v-model="leftDrawerOpen" show-if-above bordered class=" sidebar " :width="270"  :breakpoint="600">
      <q-scroll-area class="fit scrollarea" :thumb-style="thumbStyle" :bar-style="barStyle">

        <q-list padding class=" q-px-sm " style="">
          <q-item-label header class="text-cyan-2 ">
            Admin
          </q-item-label>
          <q-item v-for="link in links1" :key="link.text" v-ripple clickable :to="link.path"
            v-show="link.children.length == 0"
            :class="{ 'active-menu': $route.path === link.path && $route.path !== '/app' }">
            <q-item-section avatar>
              <q-icon color="white" :name="link.icon" />
            </q-item-section>
            <q-item-section>
              <q-item-label>{{ link.text }}</q-item-label>
            </q-item-section>
          </q-item>
          <q-expansion-item v-for="(menu, i) in links1" :key="i" expand-separator :icon="menu.icon" :label="menu.text"
            v-show="menu?.children.length > 0" hide-expand-icon>
            <q-item v-for="(link, i) in menu.children" :key="i" v-ripple clickable
              style="margin-left: 2px" :to="link.path" :class="{ 'active-menu': $route.path === link.path }">
              <q-item-section avatar>
                <q-icon color="white" name="arrow_right_alt" />
              </q-item-section>
              <q-item-section>
                <q-item-label>{{ link.title }}</q-item-label>
              </q-item-section>
            </q-item>
          </q-expansion-item>

          <!-- <q-separator class="q-my-md text-white" /> -->

          <q-item-label header class="text-cyan-2 q-mt-sm">
            Agents & Agences
          </q-item-label>

          <q-expansion-item v-for="(menu, i) in links2" :key="i" expand-separator :icon="menu.icon" :label="menu.text"
            v-show="menu?.children.length > 0" hide-expand-icon>
            <q-item v-for="(link, i) in menu.children" :key="i" v-ripple clickable
              style="margin-left: 2px" :to="link.path" :class="{ 'active-menu': $route.path === link.path }">
              <q-item-section avatar>
                <q-icon color="white" name="arrow_right_alt" />
              </q-item-section>
              <q-item-section>
                <q-item-label>{{ link.title }}</q-item-label>
              </q-item-section>
            </q-item>
          </q-expansion-item>

          <q-item v-for="(link, i) in links2" :key="i" v-ripple clickable v-show="link.children.length <= 0"
            :to="link.path" :class="{ 'active-menu': $route.path === link.path }">
            <q-item-section avatar>
              <q-icon color="white" :name="link.icon" />
            </q-item-section>
            <q-item-section>
              <q-item-label>{{ link.text }}</q-item-label>
            </q-item-section>
          </q-item>

          <!-- <q-separator class="q-mt-md q-mb-xs" /> -->

          <q-item-label header class="text-cyan-2 q-mt-sm">
            Comptabilité
          </q-item-label>

          <q-item v-for="link in links3" :key="link.text" v-ripple clickable :to="link.path"
            :class="{ 'active-menu': $route.path === link.path }">
            <q-item-section avatar>
              <q-icon color="white" :name="link.icon" />
            </q-item-section>
            <q-item-section>
              <q-item-label>{{ link.text }}</q-item-label>
            </q-item-section>
          </q-item>

          <!-- <q-separator class="q-my-md" /> -->

          <q-item-label header class="text-cyan-2 q-mt-sm">
            Stock & Livraisons
          </q-item-label>

          <q-expansion-item v-for="(menu, i) in links4" :key="i" expand-separator :icon="menu.icon" :label="menu.text"
            v-show="menu?.children.length > 0" hide-expand-icon>
            <q-item v-for="(link, i) in menu.children" :key="i" v-ripple clickable
              style="margin-left: 2px" :to="link.path" :class="{ 'active-menu': $route.path === link.path }">
              <q-item-section avatar>
                <q-icon color="white" name="arrow_right_alt" />
              </q-item-section>
              <q-item-section>
                <q-item-label>{{ link.title }}</q-item-label>
              </q-item-section>
            </q-item>
          </q-expansion-item>
          <q-item v-for="link in links4" :key="link.text" v-ripple clickable v-show="link.children.length <= 0"
            :to="link.path" :class="{ 'active-menu': $route.path === link.path }">
            <q-item-section avatar>
              <q-icon color="white" :name="link.icon" />
            </q-item-section>
            <q-item-section>
              <q-item-label>{{ link.text }}</q-item-label>
            </q-item-section>
          </q-item>

          <q-item-label header class="text-cyan-2 q-mt-sm">
            Configuration
          </q-item-label>

          <q-expansion-item v-for="(menu, i) in links5" :key="i" expand-separator :icon="menu.icon" :label="menu.text"
            v-show="menu?.children.length > 0" hide-expand-icon>
            <q-item v-for="(link, i) in menu.children" :key="i" v-ripple clickable
              style="margin-left: 2px" :to="link.path" :class="{ 'active-menu': $route.path === link.path }">
              <q-item-section avatar>
                <q-icon color="white" name="arrow_right_alt" />
              </q-item-section>
              <q-item-section>
                <q-item-label>{{ link.title }}</q-item-label>
              </q-item-section>
            </q-item>
          </q-expansion-item>
          <q-item v-for="link in links5" :key="link.text" v-ripple clickable v-show="link.children.length <= 0"
            :to="link.path" :class="{ 'active-menu': $route.path === link.path }">
            <q-item-section avatar>
              <q-icon color="white" :name="link.icon" />
            </q-item-section>
            <q-item-section>
              <q-item-label>{{ link.text }}</q-item-label>
            </q-item-section>
          </q-item>

          <div class="q-py-md q-px-md text-grey-9">
            <div class="row items-center q-gutter-x-sm q-gutter-y-xs">
              <a href="" class="YL__drawer-footer-link">
                <q-icon name="copyright" color="cyan-2" />
                <span class="text-cyan-2 q-mt-sm">FRIKALAB-GROUP</span>
              </a>
            </div>
          </div>
        </q-list>
      </q-scroll-area>
      <div class="absolute-top img-header ">
        <div style="display: flex; align-items: center;">
          <q-avatar size="50px" style="margin-right: 8px;">
            <q-img :src="logo" cover />
          </q-avatar>
          <div>
            <span class="text-h6 text-white">IZICOLLECT</span>
            <!-- <span class="text-subtitle2 text-white">v1.0</span> -->
          </div>
        </div>
      </div>
    </q-drawer>

    <q-page-container class="main">
      <router-view />
    </q-page-container>
  </q-layout>
</template>



<style>
.active-menu {
  border-left: 5px solid white;
  color: #FFFFFF !important;
  border-radius: 5px;
  font-weight: lighter;
  /* background: white; */
  /* color: #3f51b5; */
}

.main {
  background-color: #FFFFFF;
  overflow: hidden;
}

.scrollarea {
  height: calc(100% - 100px);
  margin-top: 60px;
  border-right: 0px solid #ddd;
  overflow-y: auto;
}

.sidebar {
  background: #1E5EFF;
  /* background: linear-gradient(90deg, rgb(7, 50, 158) 43%, rgba(0, 212, 255, 1) 100%); */
  height: 100vh;
  overflow-y: hidden;
  color: #C2C0FF
}

.absolute-top {
  display: flex;
  align-items: center;
  justify-content: left;
  flex-direction: column;
  position: absolute;

}

.img-header {
  background: #1E5EFF;
  width: 100%;
  height: 60px;
  display: flex;
  align-items: start;
  justify-content: center;
  padding: 15px;
  flex-direction: column;
}
</style>
