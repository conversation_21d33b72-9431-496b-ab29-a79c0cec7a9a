<script lang="ts">
import { defineComponent, ref, onMounted, computed } from 'vue';
import { storeToRefs } from 'pinia';
import { Personal } from 'src/models';
import { productStore } from 'src/stores/core/productStore';
import { packStore } from 'src/stores/core/packStore';
import { useQuasar } from 'quasar';

export default defineComponent({
  name: "AddPackModal",
  components: {},
  setup() {
    const $q = useQuasar();
    const dialog = ref(false);
    const showLoading = ref(false);
    const disabled = ref(false);

    const prod_store = productStore();
    const { products, categories } = storeToRefs(productStore());
    const { getProducts, getCategories } = prod_store;

    const pack_store = packStore();
    const { addPack, getPacks } = pack_store;

    const packs = ref([
      {
        id: 1,
        product_id: "",
        price: "" as any,
        quantity: 1,
        category_id: "",
        product_name: "",
        products: [] as any,
      }
    ]);

    let pack_name = ref("");
    let total_price = ref(0);

    const form = ref({
      products: packs.value,
      name: "",
      category: "",
      tarif: "" as any,
      description: "",
      total_price: "" as any,
    });

    const add_packs = () => {
      packs.value.push({
        id: packs.value.length + 1,
        product_id: "",
        price: "" as any,
        quantity: 1,
        category_id: "",
        product_name: "",
        products: []
      });
    };

    const get_products_by_catItem = (item: any) => {
      if (item.category_id) {
        console.log("category_id", item.category_id);

        item.products = products.value.filter((product) => product.category_id === item.category_id);
      } else {
        item.products = products.value;
      }
    };

    const get_product_by_packItem = (item: any) => {
      const prod = products.value.find((product) => product.id === item.product_id);
      if (prod) {
        item.price = prod.price_vente;
        item.category_id = prod.category_id;
        item.product_name = prod.name;
        updateTotalPrice();
      }
    };

    const onQuantityChange = (item: any) => {
      updateTotalPrice();
    };

    const updateTotalPrice = () => {
      total_price.value = packs.value.reduce((total, item) => total + (item.price * item.quantity), 0);
      form.value.total_price = total_price.value;

      const nbre_days = 31 * 12;
      let tarif = total_price.value / nbre_days;
      tarif = Math.ceil(tarif);
      form.value.tarif = tarif.toFixed(2);
    };

    const remove_pack = (item: any) => {
      packs.value = packs.value.filter((pack) => pack.id !== item.id);
      updateTotalPrice();
    };

    const onSubmit = async () => {
      disabled.value = true;
      showLoading.value = true;
      try {
        let items = packs.value.map(element => ({
          product_id: element.product_id,
          quantity: element.quantity,
          price: element.price,
        }));
        form.value.products = items as any;

        const res = await addPack(form.value);
        if (res.success) {
          $q.notify({
            color: "green",
            type: 'positive',
            icon: "check",
            position: 'top-right',
            progress: true,
            timeout: 3500,
            message: res.message
          });
          onReset();
          await getPacks();
        } else {
          $q.notify({
            color: "red-4",
            type: 'negative',
            icon: "error",
            position: 'top-right',
            progress: true,
            timeout: 3500,
            message: res.message
          });
        }
      } catch (error) {
        console.log("Error : ", error);
        $q.notify({
          color: "red-4",
          type: 'negative',
          icon: "error",
          position: 'top-right',
          progress: true,
          timeout: 3500,
          message: "Erreur lors de l'ajout du carnet"
        });
      } finally {
        showLoading.value = false;
        disabled.value = false;
      }
    };

    const onReset = () => {
      packs.value = [];
      form.value = {
        products: packs.value,
        name: "",
        category: "",
        tarif: "",
        description: "",
        total_price: "",
      };
      total_price.value = 0;
    };

    onMounted(async () => {

    });

    return {
      dialog, form, onSubmit, onReset,
      products, remove_pack, categories, packs, add_packs, get_product_by_packItem, pack_name,
      onQuantityChange, get_products_by_catItem, showLoading, disabled
    };
  }
});
</script>

<template>
  <div class="row">
    <q-btn color="primary" icon="add" label="AJOUTER UN PACK" @click="dialog = true" />
    <q-dialog v-model="dialog" persistent maximized transition-show="slide-up" transition-hide="slide-down">
      <q-card class="my-card">
        <q-form @submit="onSubmit" @reset="onReset">
          <q-toolbar class="bg-primary text-white">
            <q-toolbar-title>
              AJOUTER UN NOUVEAU PACK
            </q-toolbar-title>
            <q-btn flat round dense icon="close" class="q-mr-xs" @click="dialog = false" />
            <q-btn label="Enregistrer" color="positive" type="submit" />
          </q-toolbar>
          <q-card-section>
            <div class="row q-col-gutter-md q-mt-sm q-pb-sm">
              <div class="col col-md-6 col-sm-12 col-xs-12">
                <q-input filled v-model="form.name" label="Nom du carnet" placeholder="Nom du carnet "
                  hint="Nom du carnet, exple: 1 abc + 1 def + 1 ghi" />
              </div>
              <div class="col col-md-6 col-sm-12 col-xs-12">
                <q-input filled v-model="form.tarif" label="Tarif du carnet" placeholder="Tarif du carnet" />
              </div>
            </div>
            <q-scroll-area style="width: 100%; height: 460px;">
              <div class="row q-col-gutter-md">
                <div class="col col-md-6 col-sm-12 col-xs-12" v-for="(item, i) in packs" :key="i">
                  <q-card class="my-card">
                    <q-toolbar class="bg-primary text-white">
                      <q-btn flat round dense icon="account_circle" />
                      <q-toolbar-title>
                        {{ item.quantity }} {{ item.product_name }} à {{ item.price * item.quantity }} FCFA
                      </q-toolbar-title>
                      <q-btn flat round dense icon="delete" :disable="packs.length === 1" class="q-mr-xs"
                        @click="remove_pack(item)" />
                      <q-btn flat round dense icon="add" @click="add_packs" />
                    </q-toolbar>
                    <q-card-section>
                      <div class="row q-col-gutter-md">
                        <div class="col col-md-6 col-sm-12 col-xs-12">
                          <q-select filled v-model="item.category_id" :options="categories" label="Catégorie de produit"
                            hint="Catégorie" lazy-rules aria-placeholder="Catégorie" option-label="name"
                            option-value="id" map-options emit-value
                            @update:model-value="get_products_by_catItem(item)" />
                        </div>

                        <div class="col col-md-6 col-sm-12 col-xs-12">
                          <q-select filled v-model="item.product_id" :options="item.products" label="Produit"
                            hint="Produit" lazy-rules placeholder="Produit" option-label="name" option-value="id"
                            :rules="[(val:any) => !!val || 'Veuillez choisir un produit']" map-options emit-value
                            @update:model-value="get_product_by_packItem(item)" />
                        </div>

                        <div class="col col-md-6 col-sm-12 col-xs-12">
                          <q-input filled v-model="item.price" label="Prix de vente" readonly hint="Prix de vente"
                            lazy-rules placeholder="Prix de vente" />
                        </div>
                        <div class="col col-md-6 col-sm-12 col-xs-12">
                          <q-input filled v-model="item.quantity" label="Quantité" type="number" hint="Quantité"
                            placeholder="Quantité" @update:model-value="onQuantityChange(item)" />
                        </div>
                      </div>
                    </q-card-section>
                  </q-card>
                </div>
              </div>
            </q-scroll-area>
          </q-card-section>
          <q-card-actions align="right">
            <q-btn flat label="Annuler" color="primary" @click="dialog = false" />
            <q-btn label="Enregistrer" color="positive" type="submit" :disable="disabled" />
          </q-card-actions>
          <q-inner-loading :showing="showLoading">
            <q-spinner-gears size="50px" color="primary" />
          </q-inner-loading>
        </q-form>
      </q-card>
    </q-dialog>
  </div>
</template>
