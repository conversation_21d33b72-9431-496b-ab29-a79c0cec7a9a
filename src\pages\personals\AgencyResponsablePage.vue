<script lang="ts">
import { defineComponent, onMounted, ref } from 'vue';
import BreadCrumb from 'src/layouts/BreadCrumb.vue';
import PersonalTableComponent from 'src/components/tables/PersonalTableComponent.vue';
import AddPersonalModal from 'src/components/modals/AddPersonalModal.vue';
export default defineComponent({
  name: "AgencyResponsablePage",
  components: {
    BreadCrumb, PersonalTableComponent,AddPersonalModal,
  },
  setup() {
    const bread = ref({
      pageTitle: "Responsable d'agence",
      subTitle: "Gestion des responsables d'agence"
    });

    const roleId = ref(3);

    onMounted(async () => {

    });

    return {
      bread, roleId
    };

  }
})
</script>
<template>
  <q-page class="q-pa-sm">
    <BreadCrumb :bread="bread" />
    <div class="row q-col-gutter-sm">
      <div class="col col-md-12 col-sm-12">
        <q-card class="my-card" flat>
          <q-toolbar class=" text-dark">
            <q-toolbar-title>
              Gestion Responsable d'agence
            </q-toolbar-title>
            <AddPersonalModal :role-id="roleId"  />
          </q-toolbar>
          <PersonalTableComponent :role-id="roleId" />
        </q-card>
      </div>
    </div>
  </q-page>
</template>
