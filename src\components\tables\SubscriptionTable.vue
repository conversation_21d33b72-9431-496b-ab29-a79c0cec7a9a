<script lang="ts">
import { storeToRefs } from 'pinia';
import { adminStore } from 'src/stores/core/adminStore';
import { defineComponent, onMounted, ref } from 'vue';
import { get_amount_format, get_date_format, get_status_format } from 'src/helpers/utils';

export default defineComponent({
  name: "SubscriptionTable",
  setup() {
    const pagination = ref({
      sortBy: 'desc',
      descending: false,
      page: 1,
      rowsPerPage: 20,
      rowsNumber: 20
    });
    const filter = ref('');
    const loading = ref(false);

    const status = ref([
      { label: 'Tous', value: '' },
      { label: 'En attente', value: 'pending' },
      { label: 'Démarrer', value: 'started' },
      { label: 'Terminé', value: 'finished' },
      { label: 'Livré', value: 'delivered' },
      { label: 'Annulé', value: 'canceled' }
    ]);
    const filterStatus = ref({ label: 'Tous', value: '' });

    const store = adminStore();
    const { subscriptions } = storeToRefs(adminStore());
    const { getSubscriptions } = store;

    const headers = [
      { name: 'code', label: 'CODE CARNET', field: 'code', sortable: true, align: "left" },
      { name: 'client', label: 'CLIENT', field: 'client', sortable: true, align: "left" },
      { name: 'agency', label: 'AGENCE ', field: 'description', sortable: true, align: "left" },
      { name: 'collector', label: 'AGENT COLLECTEUR', field: 'collector', sortable: true, align: "left" },
      { name: 'pack', label: 'PACK', field: 'pack', sortable: true, align: "left" },
      { name: 'price', label: 'MISE DU CARNET', field: 'price', sortable: true, align: "left" },
      { name: 'cotisation', label: 'MONTANT COTISER', field: 'cotisation', sortable: true, align: "left" },
      { name: 'rest_cotise', label: 'RESTE A COTISER', field: 'rest_cotise', sortable: true, align: "left" },
      { name: 'delivered_at', label: 'LIVRE LE', field: 'delivered_at', sortable: true, align: "left" },
      { name: 'created_at', label: 'DATE CREATION', field: 'created_at', sortable: true, align: "left" },
      { name: 'status', label: 'STATUS', field: 'status', sortable: true, align: "left" },
      { name: 'actions', label: 'Actions', field: 'actions', sortable: false },
    ] as any;

    const onRequest = async (props: any) => {
      loading.value = true;
      pagination.value.page = props.pagination.page;
      pagination.value.rowsPerPage = props.pagination.rowsPerPage;

      const params = {
        page: pagination.value.page,
        limit: pagination.value.rowsPerPage,
        ...(filterStatus.value.value && { status: filterStatus.value.value })
      };

      const res = await getSubscriptions(params);

      if (res.success) {
        loading.value = false;
        pagination.value.rowsNumber = res.result.total;
        pagination.value.page = res.result.current_page;
        subscriptions.value = res.result.data;
      }
    };

    const onFilterStatus = async () => {
      loading.value = true;
      const res = await getSubscriptions({
        limit: 20,
        page: 1,
        status: filterStatus.value.value
      });
      if (res.success) {
        loading.value = false;
        pagination.value.rowsNumber = res.result.total;
        pagination.value.page = res.result.current_page;
        subscriptions.value = res.result.data;
      }
    }

    onMounted(async () => {
      loading.value = true;
      const res = await getSubscriptions({
        limit: 20,
        page: 1
      });
      if (res.success) {
        loading.value = false;
        pagination.value.rowsNumber = res.result.total;
        pagination.value.page = res.result.current_page;
        subscriptions.value = res.result.data;
      } else {
        loading.value = false;
      }
    });

    return {
      pagination, filter, loading, headers, subscriptions, get_amount_format, get_date_format, get_status_format, onRequest, status, filterStatus, onFilterStatus
    };

  }
});
</script>

<template>
  <div class="q-pa-sm">
    <q-table flat bordered title="Liste des carnets" :rows="subscriptions" :columns="headers" row-key="name"
      v-model:pagination="pagination" :filter="filter" table-style="max-width: 100%;" :loading="loading"
      table-class="my-sticky-header-table" @request="onRequest">
      <template v-slot:top-right="props">
        <q-btn flat round dense :icon="props.inFullscreen ? 'fullscreen_exit' : 'fullscreen'"
          @click="props.toggleFullscreen" class="q-ml-md" />
        <q-input filled dense debounce="300" v-model="filter" clearable clear-icon="close"
          placeholder="Rechercher un versement">
          <template v-slot:append>
            <q-icon name="search" />
          </template>
        </q-input>
        <q-select v-model="filterStatus" :options="status" label="Statut" filled dense class="q-ml-md"
          @update:model-value="onFilterStatus" />
      </template>

      <template v-slot:no-data="{ icon, message, }">
        <div class="full-width row flex-center text-accent q-gutter-sm">
          <q-icon size="2em" name="sentiment_dissatisfied" />
          <span>
            Aucune catégorie trouvée
          </span>
          <q-icon size="2em" :name="filter ? 'filter_b_and_w' : icon" />
        </div>
      </template>
      <template v-slot:body="props">
        <q-tr :props="props">
          <q-td key="code" :props="props">
            <router-link :to="{ name: 'detail-carnet', params: { code: props.row.code } }">
              {{ props.row.code }}
            </router-link>
          </q-td>
          <q-td key="client" :props="props">
            <router-link :to="{ name: 'detail-client', params: { code: props.row.code } }">
              {{ props.row.client?.nom }} {{ props.row.client?.prenoms }}
            </router-link>
          </q-td>

          <q-td key="agency" :props="props">
            {{ props.row.agency?.name }}
          </q-td>
          <q-td key="collector" :props="props">
            {{ props.row.collector?.nom }} {{ props.row.collector?.prenoms }}
          </q-td>
          <q-td key="pack" :props="props">
            <router-link :to="{ name: 'detail-pack', params: { code: props.row.pack?.id } }">
              {{ props.row.pack?.name?.substring(0, 50) + "..." }}
            </router-link>
          </q-td>
          <q-td key="price" :props="props">
            {{ get_amount_format(props.row.price) }}
          </q-td>
          <q-td key="cotisation" :props="props">
            {{ props.row?.cotisation !== null ? get_amount_format(props.row?.cotisation?.total_amount) : 0 }}
          </q-td>
          <q-td key="rest_cotise" :props="props">
            {{ props.row?.cotisation !== null ? get_amount_format(props.row?.pack?.total_price -
              props.row?.cotisation?.total_amount) : get_amount_format(props.row?.pack?.total_price) }}
          </q-td>

          <q-td key="delivered_at" :props="props">
            {{ props.row.status === "delivered" ? get_date_format(props.row.delivered_at) : "" }}
          </q-td>
          <q-td key="created_at" :props="props">
            {{ get_date_format(props.row.created_at) }}
          </q-td>
          <q-td key="status" :props="props">
            <q-chip :color="get_status_format(props.row.status).color" text-color="white">
              {{ get_status_format(props.row.status).text }}
            </q-chip>
          </q-td>

          <q-td key="actions" :props="props">
            <q-btn dense flat color="primary" icon="visibility" class="q-mr-xs" />
          </q-td>
        </q-tr>
      </template>
    </q-table>
  </div>
</template>
