<script lang="ts">
  import { defineComponent, onMounted, ref } from 'vue';
  import BreadCrumb from 'src/layouts/BreadCrumb.vue';
  import { useRoute } from 'vue-router';
  import { adminStore } from 'src/stores/core/adminStore';
  import { storeToRefs } from 'pinia';
  import { get_date_format,get_amount_format } from 'src/helpers/utils';
  import PersonalTableComponent from 'src/components/tables/PersonalTableComponent.vue';
  import AgencyClients from './AgencyClients.vue';
  import AgencySubscription from './AgencySubscription.vue';
  import AgencyCotisation from './AgencyCotisation.vue';
  import {Personal} from 'src/models'
  export default defineComponent({
    name: 'DetailAgencyPage',
    components:{
      BreadCrumb,PersonalTableComponent,AgencyClients,AgencySubscription,AgencyCotisation
    },
    setup(){

      const route = useRoute();
      const agency_id = String(route.params.code);
      console.log("agency_code",route.params);

      const store = adminStore();
      const  {agency} = storeToRefs(adminStore());
      const {get_agency,getDetailAgency} = store;
      const agency_info = get_agency(agency_id);
      console.log("Get agency info",agency);
      const tab = ref('personals');
      const personals = ref([]) as any;
      const clients = ref([]) as any;
      const subscriptions = ref([]) as any;
      const cotisations = ref([]) as any;
      const versements = ref([]) as any;

      const bread = ref({
        pageTitle: "Gestion agences",
        subTitle: agency_info?.name,
      });

      onMounted(async()=>{
        await getDetailAgency(Number(agency_info?.id));
        console.log("agency info",agency_info);
        console.log("agency",agency.value);
        
        bread.value.subTitle = agency_info?.name;
        if (agency.value !== undefined || agency.value !== "") {
          tab.value = 'personals';
          personals.value = agency.value.personals;
          clients.value = agency.value.clients;
          subscriptions.value = agency.value.subscriptions;
          cotisations.value = agency.value.cotisations;
          versements.value = agency.value.versements;

        }

      });

      return {
        bread,agency_info,agency,get_date_format,get_amount_format,tab,personals,clients,subscriptions,
        cotisations,versements
      }
    }
  })
</script>
<template>
  <q-page class="q-pa-md">
    <BreadCrumb :bread="bread" />
    <div class="row q-col-gutter-sm">
      <div class="col col-md-4 col-sm-12 col-xs-12">
        <q-card class="my-card">
          <img src="https://cdn.quasar.dev/img/mountains.jpg">
          <q-card-section>
            <div class="text-h6">{{ agency_info?.name }}</div>
            <div class="text-subtitle2">Crée le {{ get_date_format(agency_info?.created_at) }}</div>
            <div class="text-subtitle3">CODE: {{ agency_info?.code }} </div>
          </q-card-section>
          <q-card-section>
            <div class="row q-col-gutter-sm">

              <div class="col col-md-12 col-sm-12 col-xs-12">
                <q-input :model-value="agency_info?.phone" type="text" label="Contact" readonly />
              </div>
              <div class="col col-md-12 col-sm-12 col-xs-12">
                <q-input :model-value="agency_info?.email" type="text" label="Email" readonly />
              </div>
              <div class="col col-md-12 col-sm-12 col-xs-12">
                <q-input :model-value="agency_info?.city?.country?.name" type="text" label="Pays" readonly />
              </div>
              <div class="col col-md-12 col-sm-12 col-xs-12">
                <q-input :model-value="agency_info?.city?.name" type="text" label="Ville" readonly />
              </div>
            </div>
          </q-card-section>
        </q-card>
      </div>
      <div class="col col-md-8 col-sm-12 col-xs-12">
        <q-card class="my-card">
          <q-card-section>
            <div class="text-h6">Etat statistiques</div>
            <div class="text-subtitle2">Activités de l'agence </div>
          </q-card-section>
          <q-card-section>
            <div class="row q-col-gutter-md">
              <div class="col col-md-4 col-sm-6 col-xs-12">
                <q-card class="my-card bg-primary text-white">
                  <q-card-section>
                    <div class="text-h6 text-center">Total Client</div>
                    <div class="text-subtitle2"></div>
                  </q-card-section>
                  <q-card-section>
                    <div class="text-center text-h5">
                      {{ agency.clients?.length }}
                      <q-icon name="people" />
                    </div>
                  </q-card-section>
                </q-card>
              </div>
              <div class="col col-md-4 col-sm-6 col-xs-12">
                <q-card class="my-card ">
                  <q-card-section>
                    <div class="text-h6 text-center">Total Carnets</div>
                    <div class="text-subtitle2"></div>
                  </q-card-section>
                  <q-card-section>
                    <div class="text-center text-h5">
                      {{ agency.subscriptions?.length }}
                      <q-icon name="inventory" />
                    </div>
                  </q-card-section>
                </q-card>
              </div>
              <div class="col col-md-4 col-sm-6 col-xs-12">
                <q-card class="my-card bg-primary text-white">
                  <q-card-section>
                    <div class="text-h6 text-center">Solde</div>
                    <div class="text-subtitle2"></div>
                  </q-card-section>
                  <q-card-section>
                    <div class="text-center text-h5 q-p-sm">
                      {{ get_amount_format(agency.wallet?.balance) }}
                      <!-- <q-icon name="account_balance" /> -->
                    </div>
                  </q-card-section>
                </q-card>
              </div>
              <div class="col col-md-4 col-sm-6 col-xs-12">
                <q-card class="my-card ">
                  <q-card-section>
                    <div class="text-h6 text-center">Versements</div>
                    <div class="text-subtitle2"></div>
                  </q-card-section>
                  <q-card-section>
                    <div class="text-center text-h5">
                      {{ agency.versements?.length }}
                      <!-- <q-icon name="account_balance" /> -->
                    </div>
                  </q-card-section>
                </q-card>
              </div>
              <div class="col col-md-4 col-sm-6 col-xs-12">
                <q-card class="my-card bg-primary text-white">
                  <q-card-section>
                    <div class="text-h6 text-center">Personels</div>
                    <div class="text-subtitle2"></div>
                  </q-card-section>
                  <q-card-section>
                    <div class="text-center text-h5">
                      {{ agency.personals?.length }} <q-icon name="groups" />
                    </div>
                  </q-card-section>
                </q-card>
              </div>
              <div class="col col-md-4 col-sm-6 col-xs-12">
                <q-card class="my-card">
                  <q-card-section>
                    <div class="text-h6 text-center">Collecteurs</div>
                    <div class="text-subtitle2"></div>
                  </q-card-section>
                  <q-card-section>
                    <div class="text-center text-h5">
                      {{ agency.collectors?.length }} <q-icon name="groups" />
                    </div>
                  </q-card-section>
                </q-card>
              </div>
              <div class="col col-md-4 col-sm-6 col-xs-12">
                <q-card class="my-card bg-primary text-white">
                  <q-card-section>
                    <div class="text-h6 text-center">Caissiers</div>
                    <div class="text-subtitle2"></div>
                  </q-card-section>
                  <q-card-section>
                    <div class="text-center text-h5">
                      {{ agency.cashiers?.length }} <q-icon name="groups" />
                    </div>
                  </q-card-section>
                </q-card>
              </div>
              <div class="col col-md-4 col-sm-6 col-xs-12">
                <q-card class="my-card">
                  <q-card-section>
                    <div class="text-h6 text-center">Superviseurs</div>
                    <div class="text-subtitle2"></div>
                  </q-card-section>
                  <q-card-section>
                    <div class="text-center text-h5">
                      {{ agency.supervisors?.length }} <q-icon name="groups" />
                    </div>
                  </q-card-section>
                </q-card>
              </div>
              <div class="col col-md-4 col-sm-6 col-xs-12">
                <q-card class="my-card bg-primary text-white">
                  <q-card-section>
                    <div class="text-h6 text-center">Contrôles</div>
                    <div class="text-subtitle2"></div>
                  </q-card-section>
                  <q-card-section>
                    <div class="text-center text-h5">
                      {{ agency.controls?.length }} <q-icon name="groups" />
                    </div>
                  </q-card-section>
                </q-card>
              </div>
              <div class="col col-md-4 col-sm-6 col-xs-12">
                <q-card class="my-card">
                  <q-card-section>
                    <div class="text-h6 text-center">Total cotisés</div>
                    <div class="text-subtitle2"></div>
                  </q-card-section>
                  <q-card-section>
                    <div class="text-center text-h5">
                      {{ get_amount_format(agency.cotisations_sum_total_amount) }}
                      <!-- <q-icon name="groups" /> -->
                    </div>
                  </q-card-section>
                </q-card>
              </div>
              <div class="col col-md-4 col-sm-6 col-xs-12">
                <q-card class="my-card bg-primary text-white">
                  <q-card-section>
                    <div class="text-h6 text-center">Carnets vendus</div>
                    <div class="text-subtitle2"></div>
                  </q-card-section>
                  <q-card-section>
                    <div class="text-center text-h5">
                      {{ get_amount_format(agency.subscriptions_sum_price) }}
                      <!-- <q-icon name="money" /> -->
                    </div>
                  </q-card-section>
                </q-card>
              </div>
              <div class="col col-md-4 col-sm-6 col-xs-12">
                <q-card class="my-card">
                  <q-card-section>
                    <div class="text-h6 text-center">Total Paiements</div>
                    <div class="text-subtitle2"></div>
                  </q-card-section>
                  <q-card-section>
                    <div class="text-center text-h5">
                      {{ get_amount_format(agency.payments_sum_amount) }}
                    </div>
                  </q-card-section>
                </q-card>
              </div>
            </div>
          </q-card-section>
        </q-card>
      </div>
    </div>
    <div class="row q-col-gutter-sm q-pt-md">
      <div class="col col-md-12 col-sm-12 col-xs-12">
        <q-card class="my-card">
          <q-toolbar class="bg-primary text-white">
            <q-btn flat round dense icon="analytics" />
            <q-toolbar-title>
              HISTORIQUES DE L'AGENCE
            </q-toolbar-title>
            <q-btn flat round dense icon="apps" class="q-mr-xs" />
            <q-btn flat round dense icon="more_vert" />
          </q-toolbar>
          <q-card-section>
            <q-tabs v-model="tab"  class="text-teal" align="justify" inline-label>
              <q-tab name="personals" icon="groups" label="PERSONNELS" />
              <q-tab name="clients" icon="groups" label="CLIENTS" />
              <q-tab name="carnets" icon="box" label="CARNETS" />
              <q-tab name="cotisations" icon="account_balance_wallet" label="COTISATIONS" />
              <q-tab name="versements" icon="account_balance" label="VERSEMENTS" />
            </q-tabs>
          </q-card-section>
          <q-card-section>
            <q-tab-panels v-model="tab" animated>
              <q-tab-panel name="personals">
                <!-- <div class="text-h6">Liste des agents</div> -->
                <PersonalTableComponent :personals="personals" />
              </q-tab-panel>
              <q-tab-panel name="clients">
                <!-- <div class="text-h6">Alarms</div> -->
                <AgencyClients :clients="clients" />
              </q-tab-panel>
              <q-tab-panel name="carnets">
                <AgencySubscription :subscriptions="subscriptions" />
              </q-tab-panel>
              <q-tab-panel name="cotisations">
                <AgencyCotisation :cotisations="cotisations" />
              </q-tab-panel>
              <q-tab-panel name="versements">
                <div class="text-h6">Series</div>
                Lorem ipsum dolor sit amet consectetur adipisicing elit.
              </q-tab-panel>
            </q-tab-panels>
          </q-card-section>
        </q-card>
      </div>
    </div>
  </q-page>
</template>
