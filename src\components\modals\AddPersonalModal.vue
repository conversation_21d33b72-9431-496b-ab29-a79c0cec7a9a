<script lang="ts">
import { storeToRefs } from 'pinia';
import { useQuasar } from 'quasar';
import { Personal } from 'src/models';
import { adminStore } from 'src/stores/core/adminStore';
import { personalStore } from 'src/stores/core/personalStore';
import { computed, toRefs } from 'vue';
import { defineComponent, onMounted, ref } from 'vue';

export default defineComponent({
  name: "AddPersonalModal",
  props: {
    roleId: {
      type: Number,
      required: false
    }
  },
  setup(props) {
    const $q = useQuasar();
    const dialog = ref(false);
    const showLoading = ref(false);
    const disabled = ref(false);

    const {roleId} = toRefs(props);

    const store = personalStore();
    const { addPersonals, getPersonals } = store;

    const { cities, roles, quarters,agencies } = storeToRefs(adminStore());

    const personal = ref<Personal>({
      id: 1,
      nom: "",
      prenoms: "",
      email: "",
      phone: "",
      date_nsce: "",
      lieu_nsce: "",
      gender: "",
      situation_matrimoniale: "",
      city_id: null,
      quarter_id: null,
      agency_id: null,
      role_id: roleId.value ?? null
    });
    const genders = ref(['', 'Féminin', 'Masculin']);
    const marital_statuses = ref(['', 'Marié(e)', 'Célibataire', 'Divorcé(e)', 'Veuf(ve)', 'En couple', 'En séparation']);

    const personal_roles = computed(() => {
      return roles.value.filter((role) => {
        return role.id !== 1 && role.id !== 2 && role.id !== 8 && role.id !== 9
      });
    });

    const findRole = computed(() => {
      if (roleId.value === null || roleId.value === undefined) {
        return null;
      }
      return roles.value.find((role: any) => role.id === roleId.value);
    });

    const get_quarters_byCity = () => {
      const city = cities.value.find(city => city.id === personal.value.city_id);
      personal.value.quarters = city ? city.quarters : [];
    };

    const get_city_quarters = computed(() => {
      if (cities.value.length > 0 && personal.value.city_id) {
        personal.value.quarter_id = 0;
        return quarters.value.filter(quarter => quarter.city_id === personal.value.city_id);
      }
      return [];
    });

    const get_agencies_by_city = ()=> {
      if (cities.value.length > 0 && personal.value.city_id) {
        return agencies.value.filter(agency => agency.city_id === personal.value.city_id);
      }
      return [];
    }

    const filterAgenciesByCity = computed(() => {
      if (cities.value.length > 0 && personal.value.city_id) {
        personal.value.agency_id = null;
        return agencies.value.filter(agency => agency.city_id === personal.value.city_id);
      }
      return [];
    });

    const onSubmit = async () => {
      showLoading.value = true;
      disabled.value = true;

      try {
        const res = await addPersonals({personal: personal.value});

        if (res.success) {
          dialog.value = false;
          $q.notify({
            color: "green",
            type: 'positive',
            icon: "check",
            message: "Personnel ajouté avec succès"
          });
          await getPersonals({ page: 1, limit: 15 });
        } else {
          $q.notify({
            color: "red",
            type: 'negative',
            icon: "error",
            message: res.message
          });
        }
      } finally {
        showLoading.value = false;
        disabled.value = false;
      }
    };

    onMounted(async () => {

    });

    return {
      dialog, personal, onSubmit, roles, cities, quarters, get_quarters_byCity, showLoading, disabled,
      get_city_quarters,personal_roles,agencies,genders, marital_statuses, get_agencies_by_city,filterAgenciesByCity,findRole,
    };
  }
});
</script>

<template>
  <div class="row">
    <q-btn outline  color="primary" icon="account_circle" :label="'AJOUTER ' + (findRole?.name || 'Personnel')" @click="dialog = true" />
    <q-dialog v-model="dialog" persistent maximized transition-show="slide-up" transition-hide="slide-down">
      <q-card class="my-card">
        <q-form @submit="onSubmit">
          <q-toolbar class="bg-primary text-white">
            <q-toolbar-title>AJOUTER PERSONNEL</q-toolbar-title>
            <q-btn flat round dense icon="close" @click="dialog = false" />
            <q-btn label="Enregistrer" color="positive" type="submit" :disable="disabled" />
          </q-toolbar>
          <q-card-section>
            <!-- Form fields for a single personal -->
            <div class="row q-col-gutter-md">
              <div class="col-6">
                <q-select v-model="personal.role_id" :options="personal_roles" label="Profession" option-label="name"
                  option-value="id" map-options emit-value />
              </div>
              <!-- City and Quarter selection -->
              <div class="col-6">
                <q-select v-model="personal.city_id" :options="cities" label="Ville" option-label="name"
                  option-value="id" map-options emit-value  />
              </div>
              <div class="col-6">
                <q-select v-model="personal.quarter_id" :options="get_city_quarters" label="Quartier"
                  option-label="name" option-value="id" map-options emit-value behavior="dialog" />
              </div>
              <!-- Agency selection -->
              <div class="col-6">
                <q-select v-model="personal.agency_id" :options="filterAgenciesByCity" label="Agence" option-label="name"
                  option-value="id" map-options emit-value behavior="dialog" />
              </div>
              
              <div class="col-6">
                <q-input v-model="personal.nom" label="Nom du personnel" />
              </div>
              <div class="col-6">
                <q-input v-model="personal.prenoms" label="Prénoms du personnel" />
              </div>
              <div class="col-6">
                <q-input v-model="personal.email" label="Email" type="email" />
              </div>
              <div class="col-6">
                <q-input v-model="personal.phone" label="Téléphone" mask="########" />
              </div>
            <div class="col-6">
              <q-input v-model="personal.date_nsce" label="Date de naissance" type="date" />
            </div>
            <div class="col-6">
              <q-input v-model="personal.lieu_nsce" label="Lieu de naissance" />
            </div>
            <div class="col-6">
              <q-select v-model="personal.gender" :options="genders" label="Genre" option-label="name" option-value="id" map-options emit-value />
            </div>
            <div class="col-6">
              <q-select v-model="personal.situation_matrimoniale" :options="marital_statuses" label="Situation matrimoniale" option-label="name" option-value="id" map-options emit-value />
            </div>
              
            </div>
          </q-card-section>
        </q-form>
      </q-card>
    </q-dialog>
  </div>
</template>
