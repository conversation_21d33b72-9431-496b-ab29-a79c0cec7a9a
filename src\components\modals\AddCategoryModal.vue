<script lang="ts">
  import { defineComponent, ref } from 'vue';
  import { productStore } from 'src/stores/core/productStore';
import { useQuasar } from 'quasar';
  export default defineComponent({
    name: "AddCategoryModal",
    components: {
    },
    setup(){
      const $q = useQuasar();
      const dialog = ref(false);
      const loading = ref(false);
      const categories = ref([
        {id:1,name:"Categorie 1",description:"Description 1"},
      ]);

      const form = ref({
        categories: categories.value
      });

      const add_category = ()=>{
        categories.value.push({
          id:categories.value.length+1,
          name:"Categorie "+(categories.value.length+1),
          description:"Description "+(categories.value.length+1),
        })
      };

      const remove_category = (index:number)=>{
        categories.value.splice(index,1);
      };

      const store = productStore();
      const {getCategories,addCategories} = store;

      const onSubmit = async()=>{
        loading.value = true;
        try {
          console.log(form.value);
          const res = await addCategories(form.value);
          console.log('====================================');
          console.log('res', res);
          console.log('====================================');
          if (res.success) {
            $q.notify({
              color: "green",
              type: 'positive',
              icon: "check",
              position: 'top-right',
              progress: true,
              timeout: 3500,
              message: res.message
            });
            categories.value = [];
            form.value = {
              categories: []
            };
            const new_res = await getCategories();
            if (new_res.success) {
              loading.value = false;
              dialog.value = false;
            }
          }else{
            $q.notify({
              color: "red-4",
              type: 'negative',
              icon: "error",
              position: 'top-right',
              progress: true,
              timeout: 3500,
              message: res.message
            });
            loading.value = false;
          }
        } catch (error) {
          console.log("Error : ", error );
          loading.value = false;
          $q.notify({
            color: "red-4",
            type: 'negative',
            icon: "error",
            position: 'top-right',
            progress: true,
            timeout: 3500,
            message: "Erreur lors de l'ajout des categories"
          });
        }
      };

      const onReset = ()=>{

      };

      return {
        dialog, form,onSubmit,onReset,add_category,remove_category,categories,loading
      };

    }
  })

</script>
<template>
  <div class="row">
    <q-btn color="primary" icon="add" label="NOUVELLE CATEGORIE" @click="dialog = true" />
    <q-dialog v-model="dialog" full-height full-width persistent transition-show="slide-up" transition-hide="slide-down">
      <q-card>
        <q-form  @submit="onSubmit" @reset="onReset">
          <q-toolbar class="bg-primary text-white">
            <q-toolbar-title>
              Ajouter une nouvelle categorie
            </q-toolbar-title>
            <q-btn flat round dense icon="close" class="q-mr-xs" @click="dialog = false" />
            <q-btn  label="Enregistrer" color="positive" type="submit" />
          </q-toolbar>
          <q-card-section>
            <q-scroll-area style="width: ; height: 430px;">
              <div class="row q-col-gutter-md">
                <div class="col col-md-6 col-sm-12 col-xs-12 q-my-md" v-for="(cat,index) in categories" :key="index">
                  <q-card class="">
                    <q-toolbar class="bg-primary text-white">
                      <q-toolbar-title>
                        {{ cat.name }}
                      </q-toolbar-title>
                      <q-btn flat round dense icon="delete" :disable="categories.length == 1" class="q-mr-xs" @click="remove_category(index)" />
                      <q-btn flat round dense icon="add" class="q-mr-xs" @click="add_category" />
                    </q-toolbar>

                    <q-card-section>
                      <div class="col col-md-12 col-sm-12 col-xs-12">
                        <q-input filled v-model="cat.name" label="Nom de la catégorie"
                          hint="Nom de la catégorie" lazy-rules placeholder="Entrez le Nom de la catégorie"
                          :rules="[val => !!val || 'Veuillez saisir le nom de la catégorie']"
                        />
                      </div>
                      <div class="col col-md-12 col-sm-12 col-xs-12">
                        <q-input filled  v-model="cat.description" label="Description de la catégorie" type="textarea"
                          hint="Description de la catégorie" lazy-rules placeholder="Entrez la description de la catégorie" autogrow
                        />
                      </div>
                    </q-card-section>
                  </q-card>
                </div>
              </div>
            </q-scroll-area>
          </q-card-section>
          <q-card-actions align="right">
            <q-btn flat label="Annuler" color="primary" @click="dialog = false" />
            <q-btn  label="Enregistrer" color="positive" type="submit" />
          </q-card-actions>
          <q-inner-loading :showing="loading">
            <q-spinner-gears size="50px" color="primary" />
          </q-inner-loading>
        </q-form>
      </q-card>
    </q-dialog>
  </div>
</template>
