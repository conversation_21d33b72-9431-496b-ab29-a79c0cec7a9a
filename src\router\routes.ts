import { getToken } from 'src/helpers/myfunc';
import { RouteRecordRaw } from 'vue-router';

const routes: RouteRecordRaw[] = [
  {
    path: "/",
    name: "login",
    component: () => import('pages/auth/LoginPage.vue')
  },
  {
    name: "main",
    path: '/app',
    component: () => import('layouts/MainLayout.vue'),
    meta: { middleware: requireAuth},
    children: [
      {
        name: "dashboard",
        path: '/dashboard',
        component: () => import('pages/dashboard/Dashboard.vue')
      },
      {
        name: "agencies",
        path: '/dashboard/agencies',
        component: () => import('pages/agencies/AgencyPage.vue')
      },
      {
        name: "detail-agency",
        path: '/dashboard/agencies/:code',
        component: () => import('pages/agencies/DetailAgencyPage.vue')
      },
      {
        name: "users",
        path: '/dashboard/users',
        component: () => import('pages/users/UsersPage.vue')
      },
      {
        name: "user-detail",
        path: '/dashboard/users/:userId',
        component: () => import('pages/users/UserDetailPage.vue')
      },
      {
        name: "personals",
        path: '/dashboard/personals',
        component: () => import('pages/personals/PersonalPage.vue')
      },
      {
        name: "detail-personal",
        path: '/dashboard/personals/:id/details',
        component: () => import('pages/personals/DetailPersonalPage.vue')
      },
      {
        name: "agency_chiefs",
        path: '/dashboard/personals/agency_chiefs',
        component: () => import('pages/personals/AgencyResponsablePage.vue')
      },
      {
        name: "cashiers",
        path: '/dashboard/personals/cashiers',
        component: () => import('pages/personals/CashiersPage.vue')
      },
      {
        name: "supervisors",
        path: '/dashboard/personals/supervisors',
        component: () => import('pages/personals/SupervisorPage.vue')
      },
      {
        name: "collectors",
        path: '/dashboard/personals/collectors',
        component: () => import('pages/personals/CollectorsPage.vue')
      },
      {
        name: "livreurs",
        path: '/dashboard/personals/livreurs',
        component: () => import('pages/personals/DeliveryMenPage.vue')
      },
      {
        name: "clients",
        path: '/dashboard/clients',
        component: () => import('pages/clients/ClientPage.vue')
      },
      {
        name: "detail-client",
        path: '/dashboard/clients/:code',
        component: () => import('pages/clients/DetailClientPage.vue')
      },
      {
        name: "categories",
        path: '/dashboard/categories',
        component: () => import('pages/products/CategoryPage.vue')
      },
      {
        name: "products",
        path: '/dashboard/products',
        component: () => import('pages/products/ProductsPage.vue')
      },
      {
        name: "product-details",
        path: '/dashboard/products/:slug',
        component: () =>import('pages/products/ProductDetailPage.vue')
      },
      {
        name: "packs",
        path: '/dashboard/packs',
        component: () => import('pages/packs/PackPage.vue')
      },
      {
        name: "detail-pack",
        path: '/dashboard/packs/:code',
        component: () => import('pages/packs/DetailPackPage.vue')
      },
      {
        name: "accounting",
        path: '/dashboard/accounting',
        component: () => import('pages/accounting/AccountingPage.vue')
      },
      {
        name: "deposits",
        path: '/dashboard/deposits',
        component: () => import('pages/accounting/VersementPage.vue')
      },
      {
        name: "subscriptions",
        path: '/dashboard/subscriptions',
        component: () => import('pages/accounting/SubscriptionPage.vue')
      },
      {
        name: "carnets",
        path: '/dashboard/carnets',
        component: () => import('pages/carnets/CarnetPage.vue')
      },
      {
        name: "detail-carnet",
        path: '/dashboard/carnets/:code',
        component: () => import('pages/carnets/DetailCarnetPage.vue')
      },
      {
        name: "payments",
        path: '/dashboard/payments',
        component: () => import('pages/accounting/PaymentPage.vue')
      },
      {
        name: "cotisations",
        path: '/dashboard/cotisations',
        component: () => import('pages/accounting/CotisationPage.vue')
      },
      {
        name: "controls",
        path: '/dashboard/controls',
        component: () => import('pages/controls/ControlPage.vue')
      },
      {
        name: "countries",
        path: '/dashboard/localisation/countries',
        component: () => import('pages/state/CountryPage.vue')
      },
      {
        name: "cities",
        path: '/dashboard/localisation/cities',
        component: () => import('pages/state/CityPage.vue')
      },
      {
        name: "quarters",
        path: '/dashboard/localisation/quarters',
        component: () => import('pages/state/DistrictPage.vue')
      },
      {
        name: "settings",
        path: '/dashboard/settings',
        component: () => import('pages/settings/SettingPage.vue')
      },
      {
        name: "stocks",
        path: '/dashboard/stocks',
        component: () => import('pages/products/StockPage.vue')
      },
      {
        name: "deliveries",
        path: '/dashboard/deliveries',
        component: () => import('pages/deliveries/DeliveryPage.vue')
      }
    ],
  },

  // Always leave this as last one,
  // but you can also remove it
  {
    path: '/:catchAll(.*)*',
    component: () => import('pages/ErrorNotFound.vue'),
  },
];

function requireAuth(to: any, from: any, next: any) {
  // const isAuthenticated = localStorage.getItem('authToken');
  const token = getToken();
  if (token !== null || token !== '') {
    next();
  } else {
    next('/');
  }
}


export default routes;
