<script lang="ts">
  import { storeToRefs } from 'pinia';
import { useQuasar } from 'quasar';
import { Personal } from 'src/models';
import { adminStore } from 'src/stores/core/adminStore';
import { personalStore } from 'src/stores/core/personalStore';
  import { computed, defineComponent, ref ,onMounted} from 'vue';
  export default defineComponent({
    name: "AffectPersonalModal",
    components: {
    },
    setup(){
      const $q = useQuasar();
      const dialog = ref(false);
      const loading = ref(false);
      const showLoading = ref(false);
      const disabled = ref(false);

      const store = personalStore();
      const {getPersonalNotAffected,addMutation,getPersonals} = store;

      const admin_store = adminStore();
      const {agencies,roles} = storeToRefs(adminStore());
      const {getAgencies} = admin_store;

      const modes = ref([
        {libelle:"Affectation",value:"affectation"},
        {libelle: "Mutation",value:"mutation"}
      ]);

      const get_personals_roles = computed(()=>{
        if (roles.value.length > 0) {
          return roles.value.filter((role)=>{
            return role.id !== 1 && role.id !== 2 && role.id !== 8 && role.id !== 9
          });
        }
      });

      const personals = ref<Personal[]>([]);
      const form = ref({
        role_id: "" as any,
        personals: personals.value,
        mode: "",
        agency_id: "",
      });


      const get_personals = async ()=>{
        personals.value = [];
        if (form.value.role_id && form.value.mode) {
          showLoading.value = true;
          const res = await getPersonalNotAffected({
            role_id: form.value.role_id,
            mode: form.value.mode
          });
          if (res.success) {
            personals.value = res.result;
            personals.value.map((personal)=>{
              return {
                ...personal,
                is_affected: false
              }
            });
            console.log("personals",personals.value);

            showLoading.value = false;
          }
        }
      };

      const onSubmit = async()=>{
        console.log(form.value);
        loading.value = true;
        disabled.value = true;
        try {
          let items = [] as any;
          personals.value.forEach(element => {
            if (element.is_affected == true) {
              items.push({
                personal_id: element.id,
                role_id: form.value.role_id,
              });
            }
          });
          form.value.personals = items;
          console.log('=============onSubmit=======================');
          console.log(form.value);
          console.log('====================================');
          const res = await addMutation(form.value);
          console.log('=============res=======================');
          console.log("api response",res);
          console.log('====================================');
          if (res.message) {
            loading.value = false;
            disabled.value = false;
          }
          if (res.success) {
            $q.notify({
              color: "green",
              type: 'positive',
              icon: "check",
              position: 'top-right',
              progress: true,
              timeout: 3500,
              message: res.message
            });
            onReset();
            const new_res = await getPersonals({page: 1,limit: 15});
            if (new_res.success) {
              console.log('====================================');
              console.log(new_res.result);
              console.log('====================================');
            }
          }else{
            loading.value = false;
            $q.notify({
              color: "red-4",
              type: 'negative',
              icon: "error",
              position: 'top-right',
              progress: true,
              timeout: 3500,
              message: res.message
            });
          }
        } catch (error) {
          console.log(error);
          loading.value = false;
          disabled.value = false;
          $q.notify({
            color: "red-4",
            type: 'negative',
            icon: "error",
            position: 'top-right',
            progress: true,
            timeout: 3500,
            message: "Une erreur est survenue"
          });
        }
      };

      const onReset = ()=>{
        personals.value = [];
        form.value = {
          role_id: "" as any,
          personals: [],
          mode: "",
          agency_id: "",
        }
      };

      onMounted(async()=>{
        // await getAgencies({});
      });

      return {
        dialog, form,onSubmit,onReset,roles,personals,modes,
        agencies,get_personals_roles,get_personals,showLoading,loading,disabled
      };

    }
  })

</script>
<template>
  <div class="row">
    <q-btn flat color="" icon="settings" label="AFFECTER PERSONNELS" @click="dialog = true" />
    <q-dialog v-model="dialog" persistent maximized  transition-show="slide-up" transition-hide="slide-down">
      <q-card class="my-card">
        <q-form  @submit="onSubmit" @reset="onReset">
          <q-toolbar class="bg-primary text-white">
            <q-toolbar-title>
              AFFECTATION / MUTATION DU PERSONNEL
            </q-toolbar-title>
            <q-btn flat round dense icon="close" class="q-mr-xs" @click="dialog = false" />
            <q-btn  label="Enregistrer" color="positive" type="submit" />
          </q-toolbar>
          <q-card-section>
            <q-scroll-area style="width: 100%; height: 430px;">
              <div class="row q-col-gutter-sm">
                <div class="col col-md-12 col-sm-12 col-xs-12">
                  <q-select filled v-model="form.agency_id" :options="agencies" label="Agence"
                    lazy-rules placeholder="Choisir l'agence d'affectation" option-label="name" option-value="id" map-options emit-value
                    :rules="[val => !!val || 'Veuillez choisir une agence']"
                  />
                </div>
                <div class="col col-md-6 col-sm-12 col-xs-12">
                  <q-select filled v-model="form.mode" :options="modes" label="Mode"
                    lazy-rules placeholder="Mode" option-label="libelle" option-value="value" map-options emit-value
                    :rules="[val => !!val || 'Veuillez choisir le mode']"
                    @update:model-value="get_personals"
                  />
                </div>
                <div class="col col-md-6 col-sm-12 col-xs-12">
                  <q-select filled v-model="form.role_id" :options="get_personals_roles" label="Rôle"
                    lazy-rules placeholder="Rôle" option-label="name" option-value="id" map-options emit-value
                    :rules="[val => !!val || 'Veuillez choisir le rôle']"
                    @update:model-value="get_personals"
                  />
                </div>

              </div>
              <div class="row q-col-gutter-md">
                <div class="col col-md-12 col-sm-12 col-xs-12">
                  <q-list >
                    <q-item-label header>
                      <q-inner-loading :showing="showLoading">
                        <q-spinner-gears size="50px" color="primary" />
                      </q-inner-loading>
                    </q-item-label>
                    <q-item v-for="(item,i) in personals" :key="i" class="q-my-sm" clickable v-ripple>
                      <q-item-section avatar>
                        <q-avatar color="primary" text-color="white">
                          {{ item.nom.slice(0,1) }}
                        </q-avatar>
                      </q-item-section>
                      <q-item-section>
                        <q-item-label>{{ item.nom }} {{ item.prenoms }} </q-item-label>
                        <q-item-label caption lines="1">{{ item.email }}</q-item-label>
                        <q-item-label caption lines="2">{{ item.phone }}</q-item-label>
                      </q-item-section>
                      <q-item-section side>
                        <q-checkbox left-label v-model="item.is_affected" label="Affecté" />
                      </q-item-section>
                    </q-item>
                    <q-separator />
                  </q-list>
                </div>
              </div>
            </q-scroll-area>
          </q-card-section>
          <q-card-actions align="right">
            <q-btn flat label="Annuler" color="primary" @click="dialog = false" />
            <q-btn flat label="Réinitialiser" color="primary" @click="onReset" />
            <q-btn  label="Enregistrer" color="positive" type="submit" />
          </q-card-actions>
          <q-inner-loading :showing="loading">
            <q-spinner-gears size="50px" color="primary"
              title="Traitement en cours..."
              subtitle="Veuillez patienter..."

            />
          </q-inner-loading>
        </q-form>
      </q-card>
    </q-dialog>
  </div>
  <div class="row">

  </div>
</template>
