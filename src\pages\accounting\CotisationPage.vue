<script lang="ts">
  import { defineComponent, ref } from 'vue';
  import BreadCrumb from 'src/layouts/BreadCrumb.vue';
  import CotisationTable from 'src/components/tables/CotisationTable.vue';
  export default defineComponent({
    name: "CotisationPage",
    components: {
      BreadCrumb,CotisationTable
    },
    setup(){

      const bread = ref({
        pageTitle: "Souscriptions",
        subTitle: "Gestion des paiements"
      });

      return {
        bread
      };

    }
  })
</script>
<template>
  <q-page class="q-pa-md">
    <BreadCrumb :bread="bread" />
    <div class="row q-col-gutter-sm">
      <div class="col col-md-12 col-sm-12">
        <q-card class="my-card" flat>
          <q-toolbar class="">
            <q-toolbar-title>
              Etat des cotisations
            </q-toolbar-title>
          </q-toolbar>
          <CotisationTable />
        </q-card>
      </div>
    </div>
  </q-page>
</template>
