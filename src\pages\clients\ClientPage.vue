<script lang="ts">
  import { defineComponent, ref } from 'vue';
  import BreadCrumb from 'src/layouts/BreadCrumb.vue';
  import ClientTable from 'src/components/tables/ClientTable.vue';
  export default defineComponent({
    name: "ClientPage",
    components: {
      BreadCrumb,ClientTable
    },
    setup(){

      const bread = ref({
        pageTitle: "Clients",
        subTitle: "Gestion des clients"
      });

      return {
        bread
      };

    }
  })
</script>
<template>
  <q-page class="q-pa-sm">
    <BreadCrumb :bread="bread" />
    <div class="row q-col-gutter-sm">
      <div class="col col-md-12 col-sm-12">
        <q-card class="my-card" flat>
          <q-toolbar class=" text-dark">
            <q-toolbar-title>
              Gestion des Clients
            </q-toolbar-title>
            <q-btn color="primary"  dense icon="account_circle" label="AJOUTER CLIENT" class="q-mr-xs" />
          </q-toolbar>
          <ClientTable />
        </q-card>
      </div>
    </div>
  </q-page>
</template>
