<script lang="ts">
  import { defineComponent, ref, onMounted } from 'vue';
  import { get_amount_format, get_date_format } from 'src/helpers/utils';
  import { adminStore } from 'src/stores/core/adminStore';

  export default defineComponent({
    name: "ListVersementDay",
    
    setup() {
      const today = ref(new Date());

      const store = adminStore();
      const { getVersementsByPeriod } = store;
      const loading = ref(true);
      const period = ref("month");
      const periods = ref([
        { label: 'Jour', value: 'day' },
        { label: 'Mois', value: 'month' },
      ]);
      const versements = ref<any[]>([]);
      const initialPagination = ref({
        sortBy: 'name',
        descending: true,
        page: 1,
        rowsPerPage: 10
      });

      const headers = ref([
        { name: 'collector', label: 'COLLECTEUR', field: 'collector', sortable: true, align: "left" },
        { name: 'cashier', label: 'CAISSE', field: 'cashier', sortable: true, align: "right" },
        { name: 'agency', label: 'AGENCE', field: 'agency', sortable: true, align: "right" },
        { name: 'amount', label: 'MONTANT', field: 'amount', sortable: true, align: "right" },
        { name: 'payment_date', label: 'DATE CREATION', field: 'payment_date', sortable: true, align: "right" },
        { name: 'confirmed_at', label: 'DATE CONFIRMATION', field: 'confirmed_at', sortable: true, align: "right" },
      ]) as any;

      const filterPaymentByPeriod = async (selectedPeriod: string) => {
        loading.value = true;
        const res = await getVersementsByPeriod(selectedPeriod);
        loading.value = false;
        if (res.success) {
          versements.value = res.result.map((item: any) => ({
            ...item,
            collector: `${item.collector.nom} ${item.collector.prenoms}`,
            cashier: `${item.cashier.nom} ${item.cashier.prenoms}`,
            agency: item.agency.name,
            amount: get_amount_format(item.amount),
            payment_date: get_date_format(item.payment_date),
            confirmed_at: get_date_format(item.confirmed_at)
          }));
        } else {
          console.log("error", res);
        }
      };

      onMounted(async () => {
        await filterPaymentByPeriod(period.value);
      });

      return {
        today, get_amount_format, get_date_format, loading, versements, period, initialPagination, periods, headers, filterPaymentByPeriod
      };
    }
  });
</script>


<template>
  <div class="q-pa-md" >
    <q-table
      flat bordered
      title="Etat de versements"
      :rows="versements"
      :columns="headers"
      :loading="loading"
      row-key="id"
      :pagination="initialPagination"
      table-style="max-width: 100%;"
    >
      <template v-slot:top-right="props">
        <q-select 
          v-model="period" 
          label="Période" 
          dense 
          :options="periods" 
          emit-value 
          map-options 
          option-label="label" 
          options-dense 
          class="q-ml-md" 
          style="width: 150px" 
          @update:model-value="filterPaymentByPeriod"
        />
        <q-btn
          flat 
          round 
          dense
          :icon="props.inFullscreen ? 'fullscreen_exit' : 'fullscreen'"
          @click="props.toggleFullscreen"
          class="q-ml-md"
        />
      </template>

      <template v-slot:no-data="{ icon, message }">
        <div class="full-width row flex-center text-accent q-gutter-sm">
          <q-icon size="2em" name="sentiment_dissatisfied" />
          <span>
            Aucune donnée trouvée
          </span>
        </div>
      </template>

      <template v-slot:body="props">
        <q-tr :props="props">
          <q-td key="collector" :props="props">
            {{ props.row.collector }}
          </q-td>
          <q-td key="cashier" :props="props">
            {{ props.row.cashier }}
          </q-td>
          <q-td key="agency" :props="props">
            {{ props.row.agency }}
          </q-td>
          <q-td key="amount" :props="props">
            {{ props.row.amount }}
          </q-td>
          <q-td key="payment_date" :props="props">
            {{ props.row.payment_date }}
          </q-td>
          <q-td key="confirmed_at" :props="props">
            {{ props.row.confirmed_at }}
          </q-td>
        </q-tr>
      </template>
    </q-table>
  </div>
</template>

