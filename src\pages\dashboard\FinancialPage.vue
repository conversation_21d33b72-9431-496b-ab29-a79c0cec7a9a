<script lang="ts">
import { defineComponent,ref,onMounted } from 'vue'
import { adminStore } from 'src/stores/core/adminStore';
import { storeToRefs } from 'pinia';
import BreadCrumb from 'src/layouts/BreadCrumb.vue';

export default defineComponent({
  name: "FinancialPage",
  components: {
    BreadCrumb
  },
  setup() {

    const bread = ref({
      pageTitle: "Sante Financiere",
      subTitle: "Etat de la santé financiere"
    });

    const loading = ref(false);

    const store = adminStore();
    const {financialHealth} = storeToRefs(adminStore());
    const {getFinancialHealth} = store;

    const fetchData = async()=>{
      loading.value = true;
      const res = await getFinancialHealth();
      if (res.success) {
        financialHealth.value = res.result;
      }
      loading.value = false;
    }

    onMounted(async()=>{
      await fetchData();
    });

    return {
      bread,financialHealth,loading
    }

  },
})
</script>

<template>
  <q-page  padding>
    <BreadCrumb :bread="bread" />
    <div class="row q-col-gutter-sm">
      <div class="col col-md-12 col-sm-12">
        <q-card class="my-card" flat >
          <q-toolbar class="">
            <q-toolbar-title>
              Etat de la santé financiere
            </q-toolbar-title>
          </q-toolbar>
          <q-card-section>
            <div class="text-h6">Statistiques financières</div>
          </q-card-section>
        </q-card>
      </div>
    </div>
  </q-page>
</template>

