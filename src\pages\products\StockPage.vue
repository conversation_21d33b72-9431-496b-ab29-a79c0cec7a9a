<script lang="ts">
import BreadCrumb from 'src/layouts/BreadCrumb.vue';
import { defineComponent, ref } from 'vue';
export default defineComponent({
  name: "StockPage",
  components: {
    BreadCrumb,
  },
  setup() {

    const bread = ref({
      pageTitle: "Stocks",
      subTitle: "Gestion stocks des produits"
    });
    const tab = ref('tab1');

    return {
      bread,tab
    };

  }
})
</script>
<template>
  <q-page class="q-pa-md">
    <BreadCrumb :bread="bread" />
    <div class="row q-col-gutter-sm">
      <div class="col col-md-12 col-sm-12">
        <q-card class="my-card" flat>
          <q-toolbar class=" shadow-2 rounded-borders">
            <q-btn flat label="Gestion des stocks" />
            <q-space />
            <q-tabs v-model="tab" shrink stretch>
              <q-tab name="tab1" label="Entrées" />
              <q-tab name="tab2" label="Sorties" />
              <q-tab name="tab3" label="Etats" />
            </q-tabs>
          </q-toolbar>
        </q-card>
      </div>
    </div>
  </q-page>
</template>
