<script lang="ts">
  import { storeToRefs } from 'pinia';
import { useQuasar } from 'quasar';
import { User } from 'src/models';
import { adminStore } from 'src/stores/core/adminStore';
  import { personalStore } from 'src/stores/core/personalStore';
  import { PropType, defineComponent, onMounted, ref, toRefs } from 'vue';

  export default defineComponent({
    name: "EditUserModal",
    components: {
    },
    props:{
      user: {
        type: Object as PropType<User>,
        required: true,
        default: () => ({})

      }
    },
    setup(props){
      const dialog = ref(false);
      const showLoading = ref(false);
      const $q = useQuasar();

      const store = personalStore();
      const {roles} = storeToRefs(adminStore());
      const {updateUser} = store;

      const {user} = toRefs(props);

      const form = ref({
        user_id: "" as any,
        username: "",
        email: "",
        phone: "" as any,
        role_id: "" as any,
        status: "" as any,
      });

      const status = ref([
        { label: "Actif", value: "1" },
        { label: "Inactif", value: "0" },
      ]);

      const openDialog = ()=>{
        if (user.value  !== undefined || user.value !== "") {
          dialog.value = true;
          form.value = {
            user_id: user.value.id,
            username: user.value.username,
            email: user.value.email,
            phone: user.value.phone,
            role_id: user.value.role_id,
            status: user.value.status,
          }
        }
      }

      const onSubmit = async()=>{
        console.log(form.value);
        showLoading.value = true;
        try {
          console.log('====================================');
          console.log('form submitted',form.value);
          console.log('====================================');
          const res = await updateUser(form.value);
          console.log('=============res=======================');
          console.log("api response",res);
          console.log('====================================');
          if (res.message) {
            showLoading.value = false;
          }
          if (res.success) {
            showLoading.value = false;
            $q.notify({
              color: "green",
              type: 'positive',
              icon: "check",
              position: 'top-right',
              progress: true,
              timeout: 3500,
              message: res.message
            });
            form.value = {
              user_id: "" as any,
              username: "",
              email: "",
              phone: "" as any,
              role_id: "" as any,
              status: "",
            };
            dialog.value = false;
          }else{
            $q.notify({
              color: "red-4",
              type: 'negative',
              icon: "error",
              position: 'top-right',
              progress: true,
              timeout: 3500,
              message: res.message
            });
          }

        } catch (error) {
          console.log("Error : ", error );
          showLoading.value = false;
          $q.notify({
            color: "red-4",
            type: 'negative',
            icon: "error",
            position: 'top-right',
            progress: true,
            timeout: 3500,
            message: "Erreur d'ajout, veuillez réessayer svp"
          });
        }
      };

      const onReset = ()=>{

      };

      onMounted(async()=>{
        // await getRoles();
      });

      return {
        dialog, form,onSubmit,onReset,roles,openDialog,user,showLoading,status
      };
    }
  })

</script>
<template>
  <div class="row">
    <q-btn dense flat icon="edit" color="primary" class="q-mr-xs" @click="openDialog" />
    <q-dialog v-model="dialog" persistent full-height transition-show="slide-up" transition-hide="slide-down">
      <q-card>
        <q-form  @submit="onSubmit" @reset="onReset">
          <q-toolbar class="bg-primary text-white">
            <q-toolbar-title>
              MODIFIER LE COMPTE : {{ user.username }}
            </q-toolbar-title>
            <q-btn flat round dense icon="close" class="q-mr-xs" @click="dialog = false" />
            <q-btn  label="Enregistrer" color="positive" type="submit" />
          </q-toolbar>
          <q-card-section>
            <div class="row q-col-gutter-md">
              <div class="col col-md-12 col-sm-12 col-xs-12">
                <q-input filled v-model="form.username" label="Nom d'utilisateur" hint="Nom d'utilisateur" lazy-rules
                  aria-placeholder="Nom d'utilisateur"
                  :rules="[val => !!val || 'Veuillez saisir le nom d\'utilisateur']" />
              </div>
              <div class="col col-md-12 col-sm-12 col-xs-12">
                <q-input filled v-model="form.email" label="Email" hint="Email" lazy-rules aria-placeholder="Email"
                  :rules="[val => !!val || 'Veuillez saisir l\'email']" type="email" />
              </div>
              <div class="col col-md-12 col-sm-12 col-xs-12">
                <q-input filled v-model="form.phone" label="Téléphone" hint="Téléphone" lazy-rules
                  aria-placeholder="Téléphone" />
              </div>
            </div>
          </q-card-section>
          <q-card-actions align="right">
            <q-btn flat label="Annuler" color="primary" @click="dialog = false" />
            <q-btn  label="Enregistrer" color="positive" type="submit" />
          </q-card-actions>
          <q-inner-loading :showing="showLoading">
            <q-spinner-gears size="50px" color="primary" />
          </q-inner-loading>
        </q-form>
      </q-card>
    </q-dialog>
  </div>
</template>
