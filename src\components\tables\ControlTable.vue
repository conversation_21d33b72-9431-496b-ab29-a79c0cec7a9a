<script lang="ts">
  import { storeToRefs } from 'pinia';
  import { adminStore } from 'src/stores/core/adminStore';
  import {defineComponent,onMounted,ref} from 'vue';
  import {get_amount_format,get_date_format} from 'src/helpers/utils';

  export default defineComponent({
    name: "ControlTable",
    setup(){
      const initialPagination = ref({
        sortBy: 'name',
        descending: true,
        page: 1,
        rowsPerPage: 10
      });
      const filter = ref('');
      const loading = ref(false);

      const store = adminStore();
      const {payments,controls} = storeToRefs(adminStore());
      const {getPayments,getControls} = store;

      const headers = [
        { name: 'id', label: 'ID', field: 'id', sortable: true,align:"left" },
        { name: 'supervisor', label: 'SUPERVISEUR ', field: 'supervisor', sortable: true,align:"left" },
        { name: 'collector', label: 'AGENT COLLECTEUR', field: 'collector', sortable: true,align:"left" },
        { name: 'client', label: 'CLIENT', field: 'client', sortable: true,align:"left" },
        { name: 'subscription', label: 'CARNET(PACK)', field: 'subscription', sortable: true,align:"left" },
        { name: 'total_dette', label: 'TOTAL MTT DETTE', field: 'total_dette', sortable: true,align:"left" },
        { name: 'total_payment', label: 'TOTAL MTT PAIEMENT', field: 'total_payment', sortable: true,align:"left" },
        { name: 'created_at', label: 'DATE CREATION', field: 'created_at', sortable: true,align:"left" },
        { name: 'description', label: 'DESCRIPTION', field: 'description', sortable: true,align:"left" },
        { name: 'status', label: 'STATUS', field: 'status', sortable: true,align:"left" },
        // { name: 'actions', label: 'Actions', field: 'actions', sortable: false },
      ] as any;
      const columns_visibles = [
        'supervisor','agency','client','collector','subscription','total_payment','total_dette','payment_date',
        'status','actions','created_at','description'
      ];

      onMounted(async()=>{
        await getControls();
        if (controls.value.length > 0) {
          loading.value = false;
          console.log("controls",controls.value);
        }else{
          setTimeout(() => {
            loading.value = false;
          }, 2500);
        }
      });

      return {
        initialPagination, filter, headers,columns_visibles,payments,get_amount_format,get_date_format,
        controls
      };

    }
  });
</script>

<template>
  <div class="q-pa-md">
    <q-table
      flat bordered
      title="Liste des contrôles de carnets"
      :rows="controls"
      :columns="headers"
      row-key="name"
      :pagination="initialPagination"
      :filter="filter"
      table-style="max-width: 100%;"
      :visible-columns="columns_visibles"
    >
      <template v-slot:top-right="props">
        <q-btn
          flat round dense
          :icon="props.inFullscreen ? 'fullscreen_exit' : 'fullscreen'"
          @click="props.toggleFullscreen"
          class="q-ml-md"
        />
        <q-input filled dense debounce="300" v-model="filter" clearable clear-icon="close" placeholder="Rechercher un versement">
          <template v-slot:append>
            <q-icon name="search" />
          </template>
        </q-input>
      </template>

      <template v-slot:no-data="{ icon, message, }">
        <div class="full-width row flex-center text-accent q-gutter-sm">
          <q-icon size="2em" name="sentiment_dissatisfied" />
          <span>
            Aucune catégorie trouvée
          </span>
          <q-icon size="2em" :name="filter ? 'filter_b_and_w' : icon" />
        </div>
      </template>
      <template v-slot:body="props">
        <q-tr :props="props">
          <q-td key="supervisor" :props="props">
            {{ props.row.supervisor?.nom }} {{ props.row.supervisor?.prenoms }}
          </q-td>
          <q-td key="collector" :props="props">
            {{ props.row.collector?.nom }} {{ props.row.collector?.prenoms }}
          </q-td>
          <q-td key="client" :props="props">
            {{ props.row.client?.nom }} {{ props.row.client?.prenoms }}
          </q-td>

          <q-td key="subscription" :props="props">
            {{ props.row.subscription?.code }}
          </q-td>
          <!-- <q-td key="cotisation" :props="props">
            {{ props.row.cotisation  }}
          </q-td> -->
          <q-td key="total_dette" :props="props">
            {{ get_amount_format(props.row.total_dette)  }}
          </q-td>
          <q-td key="total_payment" :props="props">
            {{ get_amount_format(props.row.total_dette)  }}
          </q-td>
          
          <q-td key="created_at" :props="props">
            {{ get_date_format(props.row.created_at) }}
          </q-td>
          <q-td key="description" :props="props">
            {{ props.row.description }}
          </q-td>
          <q-td key="status" :props="props">
            <q-chip
              :color="props.row.status !== 'blocked' ? 'negative' : 'positive'"
              text-color="white"
            >
            {{ props.row.status }}
          </q-chip>
          </q-td>

          <q-td key="actions" :props="props">
            <q-btn dense flat color="primary" icon="visibility" class="q-mr-xs" />
          </q-td>
        </q-tr>
      </template>
  </q-table>
  </div>
</template>


