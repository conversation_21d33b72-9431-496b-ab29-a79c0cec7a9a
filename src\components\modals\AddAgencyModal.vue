<script lang="ts">
  import { storeToRefs } from 'pinia';
  import { useQuasar } from 'quasar';
  import { adminStore } from 'src/stores/core/adminStore';
  import { computed, defineComponent, onMounted, ref } from 'vue';
  export default defineComponent({
    name: "AddAgencyModal",
    setup(){
      const $q = useQuasar();
      const dialog = ref(false);
      const maximizedToggle = ref(true);
      const loading = ref(false);
      const disabled = ref(false);

      const store = adminStore();
      const {cities,quarters} = storeToRefs(adminStore());
      const {getCities,createAgency,getAgencies} = store;
      

      const form = ref({
        name: "",
        address: "",
        phone: "",
        email: "",
        city_id: "" as any,
        quarter_id: "" as any,
      });



      const get_city_quarters = computed(() => {
        if (cities.value.length > 0 && form.value.city_id) {
          form.value.quarter_id = "";
          return quarters.value.filter(quarter => quarter.city_id === form.value.city_id);
        }
        return [];
      });

      const onSubmit = async()=>{
        loading.value = true;
        disabled.value = true;
        try {
          console.log("form",form.value);
          const res = await createAgency(form.value);
          console.log("api response",res);
          if (res.message) {
            loading.value = false;
            disabled.value = false;
          }

          if (res.success) {
            $q.notify({
              color: "green",
              type: 'positive',
              icon: "check",
              position: 'top-right',
              progress: true,
              timeout: 3500,
              message: res.message
            });
            form.value= {
              name: "",
              address: "",
              phone: "",
              email: "",
              city_id: "" as any,
              quarter_id: "" as any,
            }
            const new_res = await getAgencies({page: 1,limit: 15});
            if (new_res.success) {
              dialog.value = false;
            }
          } else {
            $q.notify({
              color: "red-4",
              type: 'negative',
              icon: "error",
              position: 'top-right',
              progress: true,
              timeout: 3500,
              message: res.message
            });
          }
        } catch (error) {
          loading.value = false;
          disabled.value = false;
          console.log(error);
          $q.notify({
            color: "red-4",
            type: 'negative',
            icon: "error",
            position: 'top-right',
            progress: true,
            timeout: 3500,
            message: "Une erreur est survenue"
          });
        }
      }

      const onReset = ()=>{

      }

      onMounted(async() => {
        // await getCities();
        // console.log("cities",cities.value);
      });

      return {
        dialog, maximizedToggle, form,onSubmit,onReset,cities,quarters,get_city_quarters,
        loading,disabled
      };

    }
  })
</script>
<template>
  <div>
    <q-btn flat color="primary" icon="add" label="NOUVELLE AGENCE" @click="dialog = true" />
    <q-dialog v-model="dialog" persistent full-height  transition-show="slide-up" transition-hide="slide-down">
      <q-card class="my-card">
        <q-form  @submit="onSubmit" @reset="onReset">
          <q-toolbar class="bg-primary text-white">
            <q-toolbar-title>
              AJOUTER UNE NOUVELLE AGENCE
            </q-toolbar-title>
            <q-btn dense flat icon="close" v-close-popup>
              <q-tooltip class="bg-white text-primary">Close</q-tooltip>
            </q-btn>
            <q-btn flat outline  dense icon="save" label="ENREGISTRER" class="q-mr-xs" />
          </q-toolbar>
          <q-card-section class="">
            <div class="row">
              <div class="col col-md-12 col-sm-12 col-xs-12">
                <q-input   v-model="form.name" label="Nom Agence"
                  placeholder="Nom de l'agence" lazy-rules aria-placeholder="Nom de l'agence"
                  :rules="[val => !!val || 'Veuillez saisir le nom de l\'agence']"
                />
              </div>

              <div class="col col-md-12 col-sm-12 col-xs-12">
                <q-input   v-model="form.address" label="Adresse"
                  placeholder="Entrez l'adresse de l'agence"  aria-placeholder="Adresse de l'agence"
                />
              </div>

              <div class="col col-md-12 col-sm-12 col-xs-12">
                <q-input   v-model="form.phone" label="Contact"
                  hint="Contact de l'agence"  aria-placeholder="Contact de l'agence"
                />
              </div>

              <div class="col col-md-12 col-sm-12 col-xs-12">
                <q-input   v-model="form.email" label="Email"
                  placeholder="Entrez l'email de l'agence"  aria-placeholder="Email de l'agence"
                />
              </div>

              <div class="col col-md-12 col-sm-12 col-xs-12">
                <q-select   v-model="form.city_id" label="Ville"
                  placeholder="Choisir la ville de l'agence" lazy-rules aria-placeholder="Ville de l'agence"
                  :rules="[val => !!val || 'Veuillez choisir la ville de l\'agence']"
                  :options="cities"
                  option-value="id"
                  option-label="name"
                  map-options
                  emit-value
                  behavior="dialog"
                />
              </div>

              <div class="col col-md-12 col-sm-12 col-xs-12">
                <q-select   v-model="form.quarter_id" label="Quartier"
                  hint="Quartier de l'agence" lazy-rules aria-placeholder="Quartier de l'agence"
                  :rules="[val => !!val || 'Veuillez choisir le quartier de l\'agence']"
                  :options="get_city_quarters"
                  option-value="id"
                  option-label="name"
                  map-options
                  emit-value
                  behavior="dialog"
                >
                  <template v-slot:no-option>
                    <q-item>
                      <q-item-section class="text-grey">
                        Aucun quartier trouvé
                      </q-item-section>
                    </q-item>
                  </template>
                </q-select>
              </div>
            </div>
          </q-card-section>
          <q-card-actions align="right">
            <q-btn outline label="ANNULER" color="negative" v-close-popup />
            <q-btn  label="ENREGISTRER" outline color="positive" type="submit" :loading="loading" :disable="disabled" />
          </q-card-actions>

        </q-form>
      </q-card>
    </q-dialog>
  </div>
</template>
