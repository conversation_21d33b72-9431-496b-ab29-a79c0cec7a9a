<script lang="ts">
  import { defineComponent, ref } from 'vue';

  interface Bread{
    pageTitle?: string;
    subTitle?: string;
  }

  export default defineComponent({
    name: "BreadCrumb",
    props: {
      bread: {
        type: Object as () => Bread,
        default: () => ({ pageTitle: "", subTitle: "" }),
      },
    },
    setup(props){
      const pageTitle = ref(props.bread.pageTitle);
      const subTitle = ref(props.bread.subTitle);

      return {
        pageTitle, subTitle,
      };

    }
  })
</script>
<template>
  <q-toolbar class="bg-white text-primary q-pa-sm q-py-xs qpt-md">
    <!-- <q-toolbar-title>DASHBOARD</q-toolbar-title> -->
    <q-breadcrumbs active-color="" style="font-size: 12px">
      <q-breadcrumbs-el label="" icon="dashboard" />
      <q-breadcrumbs-el :label="pageTitle"  />
      <q-breadcrumbs-el :label="subTitle" />
    </q-breadcrumbs>
  </q-toolbar>
</template>
