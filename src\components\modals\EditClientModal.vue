<script lang="ts">
import { storeToRefs } from 'pinia';
import { useQuasar } from 'quasar';
import { User, Client } from 'src/models';
import { adminStore } from 'src/stores/core/adminStore';
import { personalStore } from 'src/stores/core/personalStore';
import { PropType, computed, defineComponent, onMounted, ref, toRefs, watch } from 'vue';

export default defineComponent({
  name: "EditClientModal",
  components: {
  },
  props: {
    client: {
      type: Object as PropType<Client>,
      required: true,
    }
  },
  setup(props) {
    const dialog = ref(false);
    const showLoading = ref(false);
    const $q = useQuasar();

    const store = personalStore();
    const { roles } = storeToRefs(adminStore());
    const { updateClient, getClients } = store;

    const { cities, quarters,agencies } = storeToRefs(adminStore());
    const { getCities } = adminStore();

    const { client } = toRefs(props);

    const form = ref({
      client_id: "" as any,
      nom: "",
      prenoms: "" as any,
      email: "" as any,
      phone: "" as any,
      status: "" as any,
      address: "" as any,
      city_id: "" as any,
      quarter_id: null as unknown as number,
      agency_id: null as unknown as number,
      profession: "" as any,
    });

    const status = ref([
      { label: "Actif", value: "1" },
      { label: "Inactif", value: "0" },
    ]);

    const openDialog = async () => {
      if (client.value !== undefined || client.value !== "") {
        console.log("open edit client dialog",client.value);

        dialog.value = true;
        form.value = {
          client_id: client.value.id,
          nom: client.value.nom,
          prenoms: client.value.prenoms,
          email: client.value.email,
          phone: client.value.phone,
          status: client.value.status,
          address: client.value.address,
          city_id: client.value.city_id,
          quarter_id: Number(client.value.quarter_id),
          profession: client.value.profession,
          agency_id: client.value.agency_id ? Number(client.value.agency_id) : 0,
        }
        console.log("edit client data",form.value);

      }
    }


    const get_city_quarters = computed(() => {
      if (cities.value.length > 0 && form.value.city_id) {
        form.value.quarter_id = null as unknown as number;
        return quarters.value.filter(quarter => quarter.city_id === form.value.city_id);
      }
      return [];
    });

    const filterAgenciesByCity = computed(() => {
      if (cities.value.length > 0 && form.value.city_id) {
        form.value.agency_id = null as unknown as number;
        return agencies.value.filter(agency => agency.city_id === form.value.city_id);
      }
      return [];
    });


    const onSubmit = async () => {
      console.log(form.value);
      showLoading.value = true;
      try {
        form.value.quarter_id = Number(client.value.quarter_id);
        form.value.agency_id = Number(client.value.agency_id);
        const res = await updateClient(form.value);
        console.log('=============res=======================');
        console.log("api response", res);
        console.log('====================================');
        if (res.message) {
          showLoading.value = false;
        }
        if (res.success) {
          showLoading.value = false;
          $q.notify({
            color: "green",
            type: 'positive',
            icon: "cloud_done",
            position: 'top-right',
            progress: true,
            timeout: 3500,
            message: res.message
          });
          form.value = {
            client_id: "" as any,
            nom: "",
            prenoms: "" as any,
            email: "" as any,
            phone: "" as any,
            status: "" as any,
            address: "" as any,
            city_id: "" as any,
            quarter_id: "" as any,
            profession: "" as any,
            agency_id: null as unknown as number,
          };
          const new_res = await getClients({ page: 1, limit: 20 });
          if (new_res.success) {
            dialog.value = false;
          }
        } else {
          showLoading.value = false;
          $q.notify({
            color: "red",
            type: 'negative',
            icon: "error",
            position: 'top-right',
            progress: true,
            timeout: 3500,
            message: res.message
          });
        }
      } catch (error) {
        console.log("Error : ", error);
        showLoading.value = false;
        $q.notify({
          color: "red",
          type: 'negative',
          icon: "error",
          position: 'top-right',
          progress: true,
          timeout: 3500,
          message: "Erreur d'ajout, veuillez réessayer svp"
        });
      }
    };

    const onReset = () => {

    };

    onMounted(async () => {

    });

    return {
      dialog, form, onSubmit, onReset, roles, openDialog, client, showLoading, status, quarters,
      get_city_quarters, cities,filterAgenciesByCity
    };

  }
})

</script>
<template>
  <div class="">
    <q-btn dense flat icon="edit" color="secondary" class="q-mr-xs" @click="openDialog" />
    <q-dialog v-model="dialog" persistent maximized transition-show="slide-up" transition-hide="slide-down">
      <q-card>
        <q-form @submit="onSubmit" @reset="onReset">
          <q-toolbar class="bg-primary text-white">
            <q-toolbar-title>
              MODIFIER LE CLIENT : {{ client.nom }} {{ client.prenoms }}
            </q-toolbar-title>
            <q-btn flat round dense icon="close" class="q-mr-xs" @click="dialog = false" />
            <q-btn label="Modifier" color="positive" type="submit" />
          </q-toolbar>
          <q-card-section>
            <q-scroll-area style="width: 100%; height: calc(100vh - 135px);">
              <div class="row q-col-gutter-md">
                <div class="col col-md-12 col-sm-12 col-xs-12">
                  <q-input v-model="form.nom" label="Nom du client" lazy-rules
                    placeholder="Entrez le nom du client"
                    :rules="[val => !!val || 'Veuillez saisir le nom du client svp']" />
                </div>
                <div class="col col-md-6 col-sm-12 col-xs-12">
                  <q-input v-model="form.prenoms" label="Prénoms du client"  lazy-rules
                    placeholder="Entrez le prénoms du client"
                    :rules="[val => !!val || 'Veuillez saisir le prenoms du client']" />
                </div>
                <div class="col col-md-6 col-sm-12 col-xs-12">
                  <q-input v-model="form.email" label="Email" hint="Email" lazy-rules placeholder="Email"
                    type="email"
                  />
                </div>
                <div class="col col-md-6 col-sm-12 col-xs-12">
                  <q-input v-model="form.phone" label="Téléphone"  lazy-rules placeholder="Téléphone"
                    :rules="[val => !!val || 'Veuillez saisir le téléphone']" mask="########" />
                </div>
                <div class="col col-md-6 col-sm-12 col-xs-12">
                  <q-input v-model="form.profession" label="Profession du client"  lazy-rules
                    placeholder="Entrez la profession du client" type="text" />
                </div>
                <div class="col col-md-6 col-sm-12 col-xs-12">
                  <q-select v-model="client.agency_id" :options="filterAgenciesByCity" label="Agence" option-label="name"
                    option-value="id" map-options emit-value behavior="dialog" />
                </div>
                <div class="col col-md-6 col-sm-12 col-xs-12">
                  <q-select dense v-model="form.city_id" label="Ville"  lazy-rules
                    placeholder="Ville de résidence"
                    :rules="[val => !!val || 'Veuillez choisir la ville de l\'agence']" :options="cities"
                    option-value="id" option-label="name" map-options emit-value behavior="dialog"
                  />
                </div>

                <div class="col col-md-6 col-sm-12 col-xs-12">
                  <q-select dense v-model="client.quarter_id" label="Quartier"  lazy-rules
                    placeholder="Quartier de résidence"
                    :rules="[val => !!val || 'Veuillez choisir le quartier du client']" :options="quarters"
                    option-value="id" option-label="name" map-options emit-value behavior="dialog" />
                </div>

                <div class="col col-md-6 col-sm-12 col-xs-12">
                  <q-select v-model="form.status" :options="status" label="Statut du client" lazy-rules
                    placeholder="Statut" option-value="value" option-label="label"
                    :rules="[val => !!val || 'Veuillez choisir le statut']" emit-value map-options />
                </div>

              </div>
            </q-scroll-area>
          </q-card-section>
          <q-card-actions align="right">
            <q-btn flat label="Annuler" color="primary" @click="dialog = false" />
            <q-btn label="Enregistrer" color="positive" type="submit" />
          </q-card-actions>
          <q-inner-loading :showing="showLoading">
            <q-spinner-gears size="50px" color="primary" />
          </q-inner-loading>
        </q-form>
      </q-card>
    </q-dialog>
  </div>
</template>
