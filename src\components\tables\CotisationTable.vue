<script lang="ts">
import { storeToRefs } from 'pinia';
import { adminStore } from 'src/stores/core/adminStore';
import { defineComponent, onMounted, ref } from 'vue';
import { get_amount_format, get_date_format, get_status_format } from 'src/helpers/utils';

export default defineComponent({
  name: "CotisationTable",
  setup() {
    const pagination = ref({
      sortBy: 'desc',
      descending: false,
      page: 1,
      rowsPerPage: 20,
      rowsNumber: 20
    });
    const filter = ref('');
    const loading = ref(false);
    const filterAgency = ref(null) as any;

    const store = adminStore();
    const { cotisations, agencies } = storeToRefs(adminStore());
    const { getCotisations } = store;

    const headers = [
      { name: 'pack', label: 'CARNET(PACK)', field: 'pack', sortable: true, align: "left" },
      { name: 'client', label: 'CLIENT', field: 'client', sortable: true, align: "left" },
      { name: 'collector', label: 'AGENT COLLECTEUR', field: 'collector', sortable: true, align: "left" },
      { name: 'agency', label: 'AGENCE', field: 'agency', sortable: true, align: "left" },
      { name: 'nbre_cotise', label: 'TOTAL CLES', field: 'nbre_cotise', sortable: true, align: "left" },
      { name: 'tarif', label: 'TARIF', field: 'tarif', sortable: true, align: "left" },
      { name: 'amount', label: 'MONTANT', field: 'amount', sortable: true, align: "left" },
      { name: 'created_at', label: 'DATE CREATION', field: 'created_at', sortable: true, align: "left" },
      { name: 'payment_date', label: 'DERNIERE COTISATION', field: 'payment_date', sortable: true, align: "left" },
      { name: 'status', label: 'STATUS', field: 'status', sortable: true, align: "left" },
      { name: 'action', label: 'ACTION', field: 'action', sortable: true, align: "left" },
    ] as any;

    const onRequest = async (props: any) => {
      loading.value = true;
      pagination.value.page = props.pagination.page;
      pagination.value.rowsPerPage = props.pagination.rowsPerPage;

      const params = {
        page: pagination.value.page,
        limit: pagination.value.rowsPerPage,
        // ...(filterStatus.value.value && { status: filterStatus.value.value })
      };
      const res = await getCotisations(params);

      if (res.success) {
        loading.value = false;
        pagination.value.rowsNumber = res.result.total;
        pagination.value.page = res.result.current_page;
        cotisations.value = res.result.data;
      }
    };

    const onFilterByAgency = async () => {
      loading.value = true;
      console.log("agency", filterAgency.value);

      const res = await getCotisations({
        limit: 20,
        page: 1,
        agency_id: filterAgency?.value?.id
      });
      if (res.success) {
        loading.value = false;
        pagination.value.rowsNumber = res.result.total;
        pagination.value.page = res.result.current_page;
        cotisations.value = res.result.data;
      }
    };

    onMounted(async () => {
      loading.value = true;
      const res = await getCotisations({
        limit: 20,
        page: 1
      });
      if (res.success) {
        loading.value = false;
        pagination.value.rowsNumber = res.result.total;
        pagination.value.page = res.result.current_page;
        cotisations.value = res.result.data;
      } else {
        loading.value = false;
      }
    });

    return {
      pagination, filter, headers, cotisations, loading, agencies, filterAgency,
      get_amount_format, get_date_format, onRequest, get_status_format,onFilterByAgency,
    };
  }
});
</script>

<template>
  <div class="q-pa-md" style="max-width: 100%">
    <q-table flat bordered title="Liste des collectes" :rows="cotisations" :columns="headers"
      v-model:pagination="pagination" :filter="filter" table-style="max-width: 100%;" :loading="loading"
      table-class="my-sticky-header-table" @request="onRequest">
      <template v-slot:top-right="props">

        <q-btn flat round dense :icon="props.inFullscreen ? 'fullscreen_exit' : 'fullscreen'"
          @click="props.toggleFullscreen" class="q-ml-md"
        />
        <q-select v-model="filterAgency" :options="agencies" label="Agences"  dense filled option-label="name"
          behavior="dialog" clearable map-options @update:model-value="onFilterByAgency"
        />
      </template>

      <template v-slot:no-data="{ icon, message, }">
        <div class="full-width row flex-center text-accent q-gutter-sm">
          <q-icon size="2em" name="sentiment_dissatisfied" />
          <span>
            Aucune donnée trouvée
          </span>
        </div>
      </template>
      <template v-slot:body="props">
        <q-tr :props="props">
          <q-td key="pack" :props="props">
            <router-link :to="{ name: 'detail-carnet', params: { code: props.row.subscription?.code } }">
              {{ props.row.subscription?.code }}
              <q-tooltip color="primary" anchor="top middle" self="bottom middle">
                {{ props.row.subscription?.pack?.name }}
              </q-tooltip>
            </router-link>
          </q-td>
          <q-td key="client" :props="props">
            <router-link :to="{ name: 'detail-client', params: { code: props.row.client?.code } }">
              {{ props.row?.client?.nom }} {{ props.row?.client?.prenoms }}
            </router-link>
          </q-td>

          <q-td key="collector" :props="props">
            {{ props.row.collector?.nom }} {{ props.row.collector?.prenoms }}
          </q-td>
          <q-td key="agency" :props="props">
            {{ props.row.agency?.name }}
          </q-td>

          <q-td key="nbre_cotise" :props="props">
            {{ props.row.nbre_cotise }}
          </q-td>
          <q-td key="tarif" :props="props">
            {{ get_amount_format(props.row.subscription?.price) }}
          </q-td>
          <q-td key="amount" :props="props">
            {{ get_amount_format(props.row.total_amount) }}
          </q-td>
          <q-td key="created_at" :props="props">
            {{ get_date_format(props.row.created_at) }}
          </q-td>
          <q-td key="payment_date" :props="props">
            {{ get_date_format(props.row.date_cotise) }}
          </q-td>
          <q-td key="status" :props="props">
            <q-chip :color="get_status_format(props.row.status).color" text-color="white">
              {{ get_status_format(props.row.status).text }}
            </q-chip>
          </q-td>
          <q-td key="action" :props="props">
            <q-btn flat dense color="primary" label="Details" />
          </q-td>
        </q-tr>
      </template>
    </q-table>
  </div>
</template>
