export interface Response{
  success: boolean;
  message: string;
  result: any;
  errors?: any;
  except?: any;
}

export interface Agency{
  id: number;
  name: string;
  address: string;
  phone?: string;
  email?: string;
  code?: string;
  status?: number;
  created_at?: string;
  responsable_id?: number;
  country_id?: number;
  city_id?: number;
  quarter_id?: number;
  localisation?: string;
  perimeters?: any;
  is_parent?: boolean;
  parent_id?: number;
  parent?: Agency;
  country?: Country;
  city?: City;
  quarter?: Quarter;
  personals?: Personal[];
  collectors?: Personal[];
  cashiers?: Personal[];
  supervisors?: Personal[];
  clients?: Client[];
  subscriptions?: Subscription[];
  controls?: Control[];
  cotisations?: Cotisation[];
  payments?: Payment[];
  versements?: Versement[];
  transactions?: Transaction[];
  wallet?: Wallet;
  annexes?: Agency[];
  responsable?: Personal;
  cotisations_sum_total_amount?: number;
  payments_sum_amount?: number;
  subscriptions_sum_price?: number;
  versements_sum_amount?: number;
}

export interface Category{
  id: number;
  name: string;
  description?: string;
  products?: Product[];
}

export interface City{
  id: number;
  name: string;
  code?: string;
  country_id?: number;
  address?: string;
  country?: Country;
  quarters?: Quarter[];
  agencies?: Agency[];
  personals?: Personal[];
  clients?: Client[];

}

export interface Country{
  id: number;
  name: string;
  prefix?: string;
  code?: string;
  cities?: City[];
  agencies?: Agency[];
}

export interface Client{
  id: number;
  nom: string;
  prenoms: string;
  email?: string;
  phone?: string;
  gender?: string;
  created_at?: string;
  address?: string;
  city_id?: number;
  quarter_id?: number;
  agency_id: number | null;
  code?: string;
  status?: string;
  localisation?: any;
  profession?: string;
  collector_id?: number;
  user_id?: number;
  photo?: string;
  agency?: Agency;
  city?: City;
  quarter?: Quarter;
  collectors?: Personal[];
  collector?: Personal[];
  user?: User;
  subscriptions?: Subscription[];
  packs?: Pack[];
  cotisations?: Cotisation[];
  controls?: Control[];
  payments?: Payment[];
}

export interface Control{
  id: number;
  supervisor_id: number;
  collector_id: number;
  client_id: number;
  date_control: string;
  status: string;
  subscription_id: number;
  description?: string;
  date_start_control: string;
  date_end_control: string;
  reason?: string;
  agency_id?: number;
  total_dette?: number;
  total_payment?: number;
  dettes?: any;
  payments?: any;
  supervisor?: Personal;
  collector?: Personal;
  client?: Client;
  subscription?: Subscription;
  agency?: Agency;

}

export interface Cotisation{
  id: number;
  subscription_id: number;
  client_id: number;
  collector_id: number;
  agency_id: number;
  date_cotise: string;
  start_at?: string;
  end_at?: string;
  nbre_cotise: number;
  status: string;
  total_amount?: number;
  description?: string;
  created_at?: string;
  subscription?: Subscription;
  client?: Client;
  collector?: Personal;
  agency?: Agency;
  payments?: Payment[];
}

export interface Analytic{
  agencies: Agency[];
  clients: Client[];
  cotisations: Cotisation[];
  versements: Versement[];
  subscriptions: Subscription[];
  payments: Payment[];
  total_cotisations: number;
  total_subscriptions: number;
  total_versements_day: number;
  total_versements: number;
  gobal_collect_sum_day: number;
  carnets_inactif: any[];
}

export interface Pack{
  id: number;
  name: string;
  description?: string;
  total_price: number;
  status?: string;
  duration?: number;
  category?: string;
  tarif?: number;
  clients? : Client[];
  products? : Product[];
}

export interface Payment{
  id: number;
  collector_id: number;
  subscription_id: number;
  cotisation_id?: number;
  agency_id: number;
  pack_id?: number;
  amount: number;
  payment_date: string;
  payment_mode?: string;
  status?: string;
  created_at?: string;
  description?: string;
  collector?: Personal;
  client?: Client;
  subscription?: Subscription;
  cotisation?: Cotisation;
  agency?: Agency;
  pack?: Pack;
}

export interface Permission{
  id: number;
  name: string;
  description?: string;
}

export interface Personal{
  id?: number;
  nom: string;
  prenoms: string;
  email?: string;
  phone?: string;
  date_nsce?: string;
  lieu_nsce?: string;
  gender?: string;
  nationality?: string;
  situation_matrimoniale?: string;
  address?: string;
  city_id: number | null;
  quarter_id: number | null;
  user_id?: number;
  status?: string;
  permissions?: any;
  created_at?: string;
  photo?: string;
  is_affected?: boolean;
  role_id: number | null;
  agency_id: number | null;
  agencies?: Agency[];
  role?: Role;
  user?: User;
  city?: City;
  quarter?: Quarter;
  clients?: Client[];
  subscriptions?: Subscription[];
  controls?: Control[];
  cotisations?: Cotisation[];
  payments?: Payment[];
  versements?: Versement[];
  cashier_versements?: Versement[];
  currentAgency?: Agency;
  current_agency?: Agency[] | null ;
  quarters?: Quarter[];
  subscriptions_sum_price?: number;
  cotisations_sum_total_amount?: number;
  versements_sum_amount?: number;
}

export interface Product{
  id: number;
  name: string;
  description?: string;
  price_achat: number;
  price_vente: number;
  profit_margin_type?: 'percent' | 'amount';
  profit_margin?: number;
  status?: string;
  category_id: number | null;
  stock_quantity?: number;
  image: string | null;
  code?: string;
  alert_quantity?: number;
  category? : Category;
  packs? : Pack[];
}

export interface Quarter{
  id: number;
  name: string;
  city_id?: number;
  description?: string;
  city?: City;
  agencies?: Agency[];
  personals?: Personal[];
  clients?: Client[];
}

export interface Role{
  id: number;
  name: string;
  description?: string;
  permissions?: any;
  personals?: Personal[];
  users?: User[];
}

export interface Subscription{
  id: number;
  client_id: number;
  pack_id: number;
  collector_id: number;
  agency_id: number;
  started_at: string;
  finished_at: string;
  status: string;
  motif?: string;
  code?: string;
  price?: number;
  created_at?: string;
  payments?: Payment[];
  cotisation?: Cotisation;
  pack?: Pack;
  client?: Client;
  collector?: Personal;
  agency?: Agency;
}

export interface Transaction{
  id: number;
  user_id: number;
  amount: number;
  description?: string;
  status?: string;
  type?: string;
  wallet_id?: number;
  date_transact?: string;
  user?: User;
  wallet?: Wallet;

}

export interface User{
  id: number;
  username: string;
  email: string;
  password: string;
  status?: string;
  role_id?: number;
  phone?: string;
  is_online?: boolean;
  secret_code?: string;
  role?: Role;
  personal?: Personal;
  client?: Client;
  transactions?: Transaction[];
  wallet?: Wallet;
}

export interface Versement{
  id: number;
  collector_id: number;
  agency_id: number;
  cashier_id?: number;
  amount: number;
  payment_date: string;
  payment_mode?: string;
  status?: string;
  qrcode?: string;
  expired_at?: string;
  token?: string;
  amount_remaining?: number;
  confirmed_at?: string;
  description?: string;
  collector?: Personal;
  agency?: Agency;
  cashier?: Personal;
}

export interface Wallet{
  id: number;
  user_id: number;
  balance: number;
  status?: string;
  type?: string;
  currency?: string;
  code?: string;
  agency_id?: number;
  user?: User;
  agency?: Agency;
  transactions?: Transaction[];
}

export interface Analytical{
  total_clients: number;
  total_carnets: number;
  total_products: number;
  total_subscriptions: number;
  total_subscriptions_amount: number;
  total_cotisations_amount: number;
  total_versements_amount: string;
  subs_by_day_count: number;
  total_versements_day: number;
  cotisations_by_day_amount: number;
  total_agencies: number;
  gobal_collect_sum_day: number;
}
