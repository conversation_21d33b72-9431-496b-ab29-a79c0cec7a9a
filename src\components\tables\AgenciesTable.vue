<script lang="ts">
import { defineComponent, ref, onMounted } from 'vue';
import { adminStore } from 'src/stores/core/adminStore';
import { storeToRefs } from 'pinia';
import EditAgencyModal from 'src/components/modals/EditAgencyModal.vue';

export default defineComponent({
  name: "AgenciesTable",
  components: {
    EditAgencyModal
  },
  setup() {
    const initialPagination = ref({
      sortBy: 'name',
      descending: true,
      page: 1,
      rowsPerPage: 10
    });
    const filter = ref('');
    const loading = ref(false);
    const pagination = ref({
      sortBy: 'desc',
      descending: false,
      page: 1,
      rowsPerPage: 10,
      rowsNumber: 20
    });

    const store = adminStore();
    const { agencies } = storeToRefs(adminStore());
    const { getAgencies } = store;

    const headers = [
      // { name: 'code', label: 'CODE AGCE', field: 'code', sortable: true, align: "left" },
      { name: 'name', label: 'NOM AGENCE', field: 'name', align: 'left', sortable: true, },
      { name: 'city', label: 'VILLE', field: 'city', sortable: true, align: "left" },
      { name: 'quarter', label: 'QUARTIER', field: 'quarter', sortable: true, align: "left" },
      { name: 'email', label: 'EMAIL', field: 'email', sortable: true, align: "left" },
      { name: 'phone', label: 'CONTACT', field: 'phone', sortable: false, align: "left" },
      { name: 'subscriptions', label: 'CARNETS VENDUS', field: 'subscriptions', sortable: false, align: "left" },
      { name: 'clients', label: 'NBRE CLIENTS', field: 'clients', sortable: true, align: "left" },
      { name: 'personals', label: 'NBRE PERSONELS', field: 'personals', sortable: true, align: "left" },
      { name: 'actions', label: 'ACTIONS', field: 'actions', sortable: false },
    ] as any;

    const onRequest = async (props: any) => {
      loading.value = true;
      console.log("pagination props",props.pagination);

      pagination.value.page = props.pagination.page;
      pagination.value.rowsPerPage = props.pagination.rowsPerPage;

      const res = await getAgencies({
        page: pagination.value.page,
        limit: pagination.value.rowsPerPage,
      });

      if (res.success) {
        loading.value = false;
        pagination.value.rowsNumber = res.result.total;
        pagination.value.page = res.result.current_page;
        agencies.value = res.result.data;
      }
    };


    onMounted(async () => {
      loading.value = true;
      const res = await getAgencies({
        limit: 15,
        page: 1
      });
      if (res.success) {
        loading.value = false;
        pagination.value.rowsNumber = res.result.to;
        pagination.value.page = res.result.current_page;
        agencies.value = res.result.data;
      }
    });


    return {
      initialPagination, filter, headers, agencies, loading,pagination
    };

  }
});
</script>

<template>
  <div class="q-pa-md">
    <q-table flat  title="Liste des agences" :rows="agencies" :columns="headers" row-key="name"
      v-model:pagination="pagination" :filter="filter" table-style="max-width: 100%;" :loading="loading">
      <template v-slot:top-right="props">
        <q-btn flat round dense :icon="props.inFullscreen ? 'fullscreen_exit' : 'fullscreen'"
          @click="props.toggleFullscreen" class="q-ml-md" />
        <q-input filled dense debounce="300" v-model="filter" clearable clear-icon="close"
          placeholder="Rechercher une agence">
          <template v-slot:append>
            <q-icon name="search" />
          </template>
        </q-input>
      </template>

      <template v-slot:no-data="{ icon, message, }">
        <div class="full-width row flex-center text-accent q-gutter-sm">
          <q-icon size="2em" name="sentiment_dissatisfied" />
          <span>
            Aucune agence trouvée
          </span>
          <q-icon size="2em" :name="filter ? 'filter_b_and_w' : icon" />
        </div>
      </template>
      <template v-slot:body="props">
        <q-tr :props="props">
          <q-td key="code" :props="props">
            {{ props.row.code }}
          </q-td>
          <q-td key="name" :props="props">
            <router-link  :to="{ name: 'detail-agency', params: {'code':  props.row?.code } }">
              {{ props.row.name }}
            </router-link>
          </q-td>
          <q-td key="city" :props="props">
            {{ props.row.city?.name }}
          </q-td>
          <q-td key="quarter" :props="props">
            {{ props.row.quarter?.name }}
          </q-td>
          <q-td key="email" :props="props">
            {{ props.row.email }}
          </q-td>
          <q-td key="phone" :props="props">
            {{ props.row.phone }}
          </q-td>
          <q-td key="subscriptions" :props="props">
            {{ props.row.subscriptions_count }}
          </q-td>
          <q-td key="clients" :props="props">
            {{ props.row.clients_count }}
          </q-td>
          <q-td key="personals" :props="props">
            {{ props.row.personals_count }}
          </q-td>
          <q-td key="actions" :props="props" style="display: flex;">
            <q-btn dense flat color="primary" icon="visibility" class="q-mr-xs"
              :to="{ name: 'detail-agency', params: { code: props.row.code } }" />
            <EditAgencyModal :agency="props.row" />
            <!-- <q-btn dense flat  color="positive" icon="account_circle" title="Nommer Responsable"/> -->
          </q-td>
        </q-tr>
      </template>
    </q-table>
  </div>
</template>
