<script lang="ts">
  import { defineComponent, ref } from 'vue';
  import BreadCrumb from 'src/layouts/BreadCrumb.vue';
  import CountryTable from 'src/components/tables/CountryTable.vue';
  import AddCategoryModal from 'src/components/modals/AddCategoryModal.vue';
  import CityTable from 'src/components/tables/CityTable.vue';
  import QuarterTable from 'src/components/tables/QuarterTable.vue';
import { adminStore } from 'src/stores/core/adminStore';
import { storeToRefs } from 'pinia';
  export default defineComponent({
    name: "CountryPage",
    components: {
      BreadCrumb,CountryTable,AddCategoryModal,CityTable,QuarterTable
    },
    setup(){

      const bread = ref({
        pageTitle: "Pays",
        subTitle: "Gestion des pays"
      });

      const store = adminStore();
      const { countries, cities, quarters } = storeToRefs(adminStore());

      return {
        bread, countries, cities, quarters
      };

    }
  })
</script>
<template>
  <q-page class="q-pa-md">
    <BreadCrumb :bread="bread" />
    <div class="row q-col-gutter-sm">
      <div class="col col-md-4 col-sm-6 col-xs-12">
        <q-card class="my-card" >
          <q-toolbar class=" text-dark">
            <q-toolbar-title>
              Pays
            </q-toolbar-title>
          </q-toolbar>
          <q-card-section>
            <div class="text-h4">{{ countries.length }}</div>
          </q-card-section>
        </q-card>
      </div>
      <div class="col col-md-4 col-sm-6 col-xs-12">
        <q-card class="my-card" >
          <q-toolbar class=" text-dark">
            <q-toolbar-title>
              Villes actives
            </q-toolbar-title>
          </q-toolbar>
          <q-card-section>
            <div class="text-h4">{{ cities.length }}</div>
          </q-card-section>
        </q-card>
      </div>
      <div class="col col-md-4 col-sm-6 col-xs-12">
        <q-card class="my-card" >
          <q-toolbar class=" text-dark">
            <q-toolbar-title>
              Quartiers actifs
            </q-toolbar-title>
          </q-toolbar>
          <q-card-section>
            <div class="text-h4">{{ quarters.length }}</div>
          </q-card-section>
        </q-card>
      </div>
      <div class="col col-md-12 col-sm-12">
        <CountryTable/>
      </div>
      <div class="col col-md-12 col-sm-12">
        <CityTable/>
      </div>
      <div class="col col-md-12 col-sm-12">
        <QuarterTable/>
      </div>
    </div>
  </q-page>
</template>
