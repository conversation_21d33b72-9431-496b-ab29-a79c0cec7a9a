<script lang="ts">
  import { storeToRefs } from 'pinia';
  import { productStore } from 'src/stores/core/productStore';
  import {defineComponent,onMounted,ref} from 'vue';
  import EditCategoryModal from '../modals/EditCategoryModal.vue';

  export default defineComponent({
    name: "CategoryTable",
    components:{EditCategoryModal},
    setup(){
      const initialPagination = ref({
        sortBy: 'name',
        descending: false,
        page: 1,
        rowsPerPage: 10
      });
      const filter = ref('');
      const loading = ref(false);

      const store = productStore();
      const {categories} = storeToRefs(productStore());
      const {getCategories} = store;

      const headers = [
        // { name: 'id', label: 'ID', field: 'id', sortable: true,hide:true,align:"left" },
        { name: 'name', label: 'LIBELLE', field: 'name', sortable: true ,align:"left" },
        { name: 'description', label: 'DESCRIPTION ', field: 'description', sortable: true,align:"left" },
        { name: 'products', label: 'PRODUITS', field: 'products', sortable: true ,align:"left"},
        { name: 'actions', label: 'ACTIONS', field: 'actions', sortable: false, align: 'left' },
      ] as any;

      onMounted(async()=>{
        loading.value = true;
        await getCategories();
        if (categories.value.length > 0) {
          loading.value = false
        } else {
          setTimeout(() => {
            loading.value = false
          }, 2500)
        }
      });

      return {
        initialPagination, filter, headers, categories,
      };

    }
  });
</script>

<template>
  <div class="q-pa-md">
    <q-table
      flat bordered
      title="Liste des catégories de produits"
      :rows="categories"
      :columns="headers"
      row-key="name"
      :pagination="initialPagination"
      :filter="filter"
      table-style="max-width: 100%;"
    >
      <template v-slot:top-right>
        <q-input filled dense debounce="300" v-model="filter" clearable clear-icon="close" placeholder="Rechercher une catégorie">
          <template v-slot:append>
            <q-icon name="search" />
          </template>
        </q-input>
      </template>

      <template v-slot:no-data="{ icon, message, }">
        <div class="full-width row flex-center text-accent q-gutter-sm">
          <q-icon size="2em" name="sentiment_dissatisfied" />
          <span>
            Aucune catégorie trouvée
          </span>
          <q-icon size="2em" :name="filter ? 'filter_b_and_w' : icon" />
        </div>
      </template>
      <template v-slot:body="props">
        <q-tr :props="props">
          <q-td key="name" :props="props">
            {{ props.row.name }}
          </q-td>
          <q-td key="description" :props="props">
            {{ props.row.description }}
          </q-td>
          <q-td key="products" :props="props">
            {{ props.row?.products?.length }}
          </q-td>
          <q-td key="actions" :props="props" style="display: flex;">
            <q-btn dense flat color="primary" icon="visibility" class="q-mr-xs" />
            <EditCategoryModal :category="props.row"/>
          </q-td>
        </q-tr>
      </template>
  </q-table>
  </div>
</template>


