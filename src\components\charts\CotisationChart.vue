<script lang="ts">
import { defineComponent, ref } from 'vue';
import VueApexCharts from 'vue3-apexcharts';

export default defineComponent({
  name: 'ApexChartComponent',
  components: {
    apexchart: VueApexCharts,
  },
  setup() {
    const series = ref([
      {
        name: 'Ventes de carnets',
        data: [44, 55, 57, 56, 61, 58, 63, 60, 66, 20, 30, 40],
      },
      {
        name: 'Cotis<PERSON>',
        data: [76, 85, 101, 98, 87, 105, 91, 114, 94, 40, 50, 60],
      },
      {
        name: 'Versements',
        data: [35, 41, 36, 26, 45, 48, 52, 53, 41, 50, 60, 70],
      },
    ]);

    const chartOptions = ref({
      chart: {
        type: 'bar',
        height: 350,
      },
      plotOptions: {
        bar: {
          horizontal: false,
          columnWidth: '55%',
          endingShape: 'rounded',
        },
      },
      dataLabels: {
        enabled: false,
      },
      stroke: {
        show: true,
        width: 2,
        colors: ['transparent'],
      },
      xaxis: {
        categories: ['Janv', 'Fév', 'Mars', 'Avr', 'Mai', 'Juin', 'Juil', 'Août', 'Sept', 'Oct', 'Nov', 'Déc'],
      },
      yaxis: {
        title: {
          text: '$ (thousands)',
        },
      },
      fill: {
        opacity: 1,
      },
      tooltip: {
        y: {
          formatter: (val: number) => `$ ${val} thousands`,
        },
      },
    });

    return {
      series,
      chartOptions,
    };
  },
});
</script>
<template>
  <div id="chart">
    <apexchart type="bar" height="350" :options="chartOptions" :series="series"></apexchart>
  </div>
</template>