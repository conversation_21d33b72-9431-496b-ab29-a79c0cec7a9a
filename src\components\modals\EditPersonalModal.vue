<script lang="ts">
import { defineComponent, ref, toRefs, PropType, watch } from 'vue';
import { useQuasar } from 'quasar';
import { Personal } from 'src/models';
import { adminStore } from 'src/stores/core/adminStore';
import { personalStore } from 'src/stores/core/personalStore';
import { storeToRefs } from 'pinia';
import { computed } from 'vue';

export default defineComponent({
  name: "EditPersonalModal",
  props: {
    personal: {
      type: Object as PropType<Personal>,
      required: true
    }
  },
  setup(props) {
    const $q = useQuasar();
    const dialog = ref(false);
    const loading = ref(false);
    const disabled = ref(false);

    const height = ref(window.innerHeight);

    const form = ref({
      nom: "",
      prenoms: "",
      email: "" as any,
      phone: "" as any,
      date_nsce: "" as any,
      lieu_nsce: "" as any,
      gender: "" as any,
      situation_matrimoniale: "" as any,
      city_id: "" as any,
      quarter_id: "" as any,
      role_id: "" as any,
      personal_id: "" as any,
      agency_id: null as unknown as number,
    });

    const store = personalStore();
    const { roles,agencies } = storeToRefs(adminStore());
    const { updatePersonal } = store;

    const { cities, quarters } = storeToRefs(adminStore());

    const { personal } = toRefs(props);

    const openDialog = () => {
      if (personal.value !== undefined || personal.value !== "") {
        dialog.value = true;
        console.log("open edit personal dialog",personal.value);
        
        form.value = {
          nom: personal.value.nom,
          prenoms: personal.value.prenoms,
          email: personal.value.email,
          phone: personal.value.phone,
          date_nsce: personal.value.date_nsce,
          lieu_nsce: personal.value.lieu_nsce,
          gender: personal.value.gender,
          situation_matrimoniale: personal.value.situation_matrimoniale,
          city_id: personal.value.city_id,
          quarter_id: Number(personal.value.quarter?.id),
          role_id: personal.value.role_id,
          personal_id: personal.value.id,
          agency_id: personal.value.current_agency ? Number(personal.value.current_agency[0].id) : 0
        }
        console.log("edit personal data",form.value);
      }
    }


    const get_city_quarters = computed(() => {
      if (cities.value.length > 0 && form.value.city_id) {
        form.value.quarter_id = "";
        return quarters.value.filter(quarter => quarter.city_id === form.value.city_id);
      }
      return [];
    });

    const filterAgenciesByCity = computed(() => {
      if (cities.value.length > 0 && personal.value.city_id) {
        personal.value.agency_id = null;
        return agencies.value.filter(agency => agency.city_id === personal.value.city_id);
      }
      return [];
    });

    
    watch(() => form.value.city_id, (newCityId) => {
      if (newCityId) {
        form.value.quarter_id = ""; 
      }
    });

    const onSubmit = async () => {
      loading.value = true;
      disabled.value = true;
      try {
        form.value.quarter_id = Number(personal.value.quarter?.id);
        console.log("form", form.value);

        const res = await updatePersonal(form.value);
        console.log("api response", res);
        if (res.message) {
          loading.value = false;
          disabled.value = false;
        }

        if (res.success) {
          $q.notify({
            color: "green",
            type: 'positive',
            icon: "cloud_done",
            position: 'top-right',
            progress: true,
            timeout: 3500,
            message: res.message
          });
          dialog.value = false;
        } else {
          $q.notify({
            color: "red-4",
            type: 'negative',
            icon: "error",
            position: 'top-right',
            progress: true,
            timeout: 3500,
            message: res.message
          });
        }
      } catch (error: any) {
        loading.value = false;
        disabled.value = false;
        console.log(error);
        $q.notify({
          color: "red-4",
          type: 'negative',
          icon: "error",
          position: 'top-right',
          progress: true,
          timeout: 3500,
          message: error.message
        })
      }
    }

    const onReset = () => {
      form.value = {
        nom: "",
        prenoms: "",
        email: "",
        phone: "",
        date_nsce: "",
        lieu_nsce: "",
        gender: "",
        situation_matrimoniale: "",
        city_id: "" as any,
        quarter_id: "" as any,
        personal_id: "" as any,
        role_id: "" as any,
        agency_id: null as unknown as number
      }
    }

    return {
      openDialog, loading, disabled, form, roles, dialog, personal, onSubmit, onReset, cities, quarters,
      get_city_quarters,filterAgenciesByCity,height,
    }
  }
})
</script>
<template>
  <div>
    <q-btn dense size="md" flat icon="edit" color="secondary" class="q-mr-xs" @click="openDialog" />
    <q-dialog v-model="dialog" persistent maximized>
      <q-card>
        <q-form @submit="onSubmit" @reset="onReset">
          <q-toolbar class="bg-primary text-white">
            <q-btn flat round dense icon="account_circle" />
            <q-toolbar-title>
              Editer le personnel: {{ personal.nom }} {{ personal.prenoms }}
            </q-toolbar-title>
            <q-btn flat round dense icon="close" class="q-mr-xs" v-close-popup />
            <q-btn flat round dense icon="save" type="submit" />
          </q-toolbar>
          <q-card-section>
            <q-scroll-area style="width: 100%; height: calc(100vh - 135px);">
              <div class="row q-col-gutter-md">
                <div class="col col-md-6 col-sm-12 col-xs-12">
                  <q-select v-model="form.role_id" :options="roles" label="Rôle" hint="Rôle" lazy-rules
                    aria-placeholder="Rôle" option-label="name" option-value="id" map-options emit-value
                    :rules="[val => !!val || 'Veuillez choisir le rôle']" />
                </div>
                <!-- Agency selection -->
                <div class="col col-md-6 col-sm-12 col-xs-12">
                  <q-select v-model="form.agency_id" :options="filterAgenciesByCity" label="Agence" option-label="name"
                    option-value="id" map-options emit-value behavior="dialog" />
                </div>
                <div class="col col-md-6 col-sm-12 col-xs-12">
                  <q-input v-model="form.nom" label="Nom du personnel" hint="Nom du personel" lazy-rules
                    aria-placeholder="Entrez le nom du personel" :rules="[val => !!val || 'Veuillez saisir le nom ']" />
                </div>
                <div class="col col-md-6 col-sm-12 col-xs-12">
                  <q-input v-model="form.prenoms" label="Prenoms du personnel" hint="Prénoms du personel" lazy-rules
                    aria-placeholder="Entrez le prenoms du personel"
                    :rules="[val => !!val || 'Veuillez saisir le prenoms']" />
                </div>
                <div class="col col-md-6 col-sm-12 col-xs-12">
                  <q-input v-model="form.email" label="Email" hint="Email" lazy-rules aria-placeholder="Email"
                    type="email" />
                </div>
                <div class="col col-md-6 col-sm-12 col-xs-12">
                  <q-input v-model="form.phone" label="Téléphone" hint="Téléphone" lazy-rules aria-placeholder="Téléphone"
                    :rules="[val => !!val || 'Veuillez saisir le téléphone']" mask="########" />
                </div>
                <div class="col col-md-6 col-sm-12 col-xs-12">
                  <q-input v-model="form.date_nsce" label="Date de naissance" hint="Date de naissance" lazy-rules
                    aria-placeholder="Date de naissance" type="date" mask="##/##/####" />
                </div>
                <div class="col col-md-6 col-sm-12 col-xs-12">
                  <q-input v-model="form.lieu_nsce" label="Lieu de naissance" hint="Lieu de naissance" lazy-rules
                    aria-placeholder="Lieu de naissance" />
                </div>
                <div class="col col-md-6 col-sm-12 col-xs-12">
                  <q-select v-model="form.gender" :options="['Masculin', 'Feminin']" label="Civilité"
                    hint="Choisir la civilité" lazy-rules aria-placeholder="Civilité" />
                </div>
                <div class="col col-md-6 col-sm-12 col-xs-12">
                  <q-select v-model="form.situation_matrimoniale"
                    :options="['Marié(e)', 'Célibataire', 'Divorcé(e)', 'Veuf(ve)', 'Concubin(e)']"
                    label="Situation Matrimoniale" hint="Situation Matrimoniale " lazy-rules
                    aria-placeholder="Situation Matrimoniale " />
                </div>
                <div class="col col-md-6 col-sm-12 col-xs-12">
                  <q-select dense v-model="form.city_id" label="Ville" hint="Ville de l'agence" lazy-rules
                    aria-placeholder="Ville de résidence"
                    :rules="[val => !!val || 'Veuillez choisir la ville de l\'agence']" :options="cities"
                    option-value="id" option-label="name" map-options emit-value behavior="dialog" />
                </div>
  
                <div class="col col-md-6 col-sm-12 col-xs-12">
                  <q-select dense v-model="personal.quarter_id" label="Quartier" hint="Quartier de l'agence" lazy-rules
                    aria-placeholder="Quartier de résidence"
                    :rules="[val => !!val || 'Veuillez choisir le quartier du personnel']" :options="get_city_quarters"
                    option-value="id" option-label="name" map-options emit-value behavior="dialog" />
                </div>
              </div>
            </q-scroll-area>
          </q-card-section>
          <q-card-actions align="right">
            <q-btn flat label="ANNULER" color="warning" v-close-popup />
            <q-btn flat label="ENREGISTRER" color="primary" type="submit" />
          </q-card-actions>
          <q-inner-loading :showing="loading" label="Traitement en cours, patientez svp..." label-class="text-teal"
            label-style="font-size: 1.1em" />
        </q-form>
      </q-card>
    </q-dialog>
  </div>
</template>
