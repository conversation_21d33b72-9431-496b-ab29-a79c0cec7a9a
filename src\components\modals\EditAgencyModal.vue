<script lang="ts">
  import { storeToRefs } from 'pinia';
  import { useQuasar } from 'quasar';
import { Agency } from 'src/models';
  import { adminStore } from 'src/stores/core/adminStore';
  import { PropType, computed, defineComponent, onMounted, ref, toRefs } from 'vue';
  export default defineComponent({
    name: "EditAgencyModal",
    props:{
      agency: {
        type: Object as PropType<Agency>,
        required: true,
      }
    },
    setup(props){
      const $q = useQuasar();
      const dialog = ref(false);
      const maximizedToggle = ref(true);
      const loading = ref(false);
      const disabled = ref(false);

      const {agency} = toRefs(props);

      const store = adminStore();
      const {cities,quarters} = storeToRefs(adminStore());
      const {getCities,updateAgency,getAgencies} = store;

      const form = ref({
        name: "",
        address: "",
        phone: "" as any,
        email: "" as any,
        city_id: "" as any,
        quarter_id: "" as any,
        agency_id: "" as any,
      });

      const openDialog = ()=>{
        if (agency.value !== undefined || agency.value !== "") {
          dialog.value = true;
          form.value = {
            name: agency.value.name,
            address: agency.value.address,
            phone: agency.value.phone,
            email: agency.value.email,
            city_id: agency.value.city_id,
            quarter_id: agency.value.quarter_id,
            agency_id: agency.value.id
          }
        }
      }

      const get_city_quarters = computed(() => {
        if (cities.value.length > 0 && form.value.city_id) {
          form.value.quarter_id = "";
          return quarters.value.filter(quarter => quarter.city_id === form.value.city_id);
        }
        return [];
      });

      const onSubmit = async()=>{
        loading.value = true;
        disabled.value = true;
        try {
          console.log("form",form.value);
          const res = await updateAgency(form.value);
          // console.log("api response",res);
          if (res.message) {
            loading.value = false;
            disabled.value = false;
          }

          if (res.success) {
            $q.notify({
              color: "green",
              type: 'positive',
              icon: "check",
              position: 'top-right',
              progress: true,
              timeout: 3500,
              message: res.message
            });
            form.value= {
              name: "",
              address: "",
              phone: "",
              email: "",
              city_id: "" as any,
              quarter_id: "" as any,
              agency_id: "" as any,
            }
            const new_res = await getAgencies({page: 1,limit: 10});
            if (new_res.success) {
              dialog.value= false;
            }
          } else {
            $q.notify({
              color: "red-4",
              type: 'negative',
              icon: "error",
              position: 'top-right',
              progress: true,
              timeout: 3500,
              message: res.message
            });
          }
        } catch (error) {
          loading.value = false;
          disabled.value = false;
          console.log(error);
          $q.notify({
            color: "red-4",
            type: 'negative',
            icon: "error",
            position: 'top-right',
            progress: true,
            timeout: 3500,
            message: "Une erreur est survenue"
          });
        }
      }

      const onReset = ()=>{

      }

      onMounted(async() => {
        // await getCities();
        // console.log("cities",cities.value);

      });

      return {
        dialog, maximizedToggle, form,onSubmit,onReset,cities,quarters,get_city_quarters,
        loading,disabled,openDialog,agency
      };

    }
  })
</script>
<template>
  <div>
    <q-btn dense flat icon="edit" color="secondary"  @click="openDialog" />
    <q-dialog v-model="dialog" persistent full-height  transition-show="slide-up" transition-hide="slide-down">
      <q-card class="my-card">
        <q-form  @submit="onSubmit" @reset="onReset">
          <q-toolbar class="bg-primary text-white">
            <q-toolbar-title>
              EDITER : {{ agency.name }}
            </q-toolbar-title>
            <q-btn dense flat icon="close" v-close-popup>
              <q-tooltip class="bg-white text-primary">Fermer</q-tooltip>
            </q-btn>
            <q-btn flat outline  dense icon="save" label="ENREGISTRER" class="q-mr-xs" />
          </q-toolbar>
          <q-card-section class="">
            <div class="row">
              <div class="col col-md-12 col-sm-12 col-xs-12">
                <q-input  v-model="form.name" label="Nom Agence"
                  hint="Nom de l'agence" lazy-rules aria-placeholder="Nom de l'agence"
                  :rules="[val => !!val || 'Veuillez saisir le nom de l\'agence']"
                />
              </div>

              <div class="col col-md-12 col-sm-12 col-xs-12">
                <q-input  v-model="form.address" label="Adresse"
                  hint="Adresse de l'agence" lazy-rules aria-placeholder="Adresse de l'agence"
                />
              </div>

              <div class="col col-md-12 col-sm-12 col-xs-12">
                <q-input  v-model="form.phone" label="Contact"
                  hint="Contact de l'agence" lazy-rules aria-placeholder="Contact de l'agence"
                />
              </div>

              <div class="col col-md-12 col-sm-12 col-xs-12">
                <q-input  v-model="form.email" label="Email"
                  hint="Email de l'agence" lazy-rules aria-placeholder="Email de l'agence"
                />
              </div>

              <div class="col col-md-12 col-sm-12 col-xs-12">
                <q-select  v-model="form.city_id" label="Ville"
                  hint="Ville de l'agence" lazy-rules aria-placeholder="Ville de l'agence"
                  :rules="[val => !!val || 'Veuillez choisir la ville de l\'agence']"
                  :options="cities"
                  option-value="id"
                  option-label="name"
                  map-options
                  emit-value
                  behavior="dialog"
                />
              </div>

              <div class="col col-md-12 col-sm-12 col-xs-12">
                <q-select  v-model="form.quarter_id" label="Quartier"
                  placeholder="Quartier de l'agence" lazy-rules aria-placeholder="Quartier de l'agence"
                  
                  :options="get_city_quarters"
                  option-value="id"
                  option-label="name"
                  map-options
                  emit-value
                  behavior="dialog"
                >
                  <template v-slot:no-option>
                    <q-item>
                      <q-item-section class="text-grey">
                        Aucun quartier trouvé
                      </q-item-section>
                    </q-item>
                  </template>
                </q-select>
              </div>
            </div>
          </q-card-section>
          <q-card-actions align="right">
            <q-btn flat label="ANNULER" color="negative" v-close-popup />
            <q-btn flat label="ENREGISTRER" color="primary" type="submit" :loading="loading" :disable="disabled" />
          </q-card-actions>

        </q-form>
      </q-card>
    </q-dialog>
  </div>
</template>
