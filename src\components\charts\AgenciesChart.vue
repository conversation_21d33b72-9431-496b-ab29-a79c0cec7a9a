<script lang="ts">
  import { defineComponent, reactive, ref } from "vue";
  import { Chart as ChartJS, ArcElement, Toolt<PERSON>, Legend } from 'chart.js';
  import { Pie } from 'vue-chartjs';

  ChartJS.register(ArcElement, Tooltip, Legend)

  export default defineComponent({
    name: 'Agencies<PERSON>hart',
    props: {
      chartConfig: {
        type: Object,
        required: false,
      }
    },
    components: {
      Pie
    },
    setup(props){
      // const {clients,registers,medecins} = reactive(props.chartConfig);
      const data = {
        labels: ['Adhérents', 'En attente','Medecin' ],
        datasets: [
          {
            backgroundColor: ['#387A05', '#E46651','#24d8cc',],
            data: [40, 25,65 ]
          }
        ]
      };
      const options = {
        responsive: true,
        maintainAspectRatio: false
      }

      return {
        data,options
      }
    }
  });
</script>
<template>
  <Pie :data="data" :options="options" />
</template>
