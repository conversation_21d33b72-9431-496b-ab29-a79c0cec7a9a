<script lang="ts">
import { useQuasar } from 'quasar';
import { authStore } from 'src/stores/auth/authStore';
import { defineComponent, ref } from 'vue';
import { useRouter } from 'vue-router';
import logo from 'src/assets/logo.jpg';
export default defineComponent({
  name: "LoginPage",
  setup() {
    const $q = useQuasar();
    const router = useRouter();
    const loading = ref(false);
    const disabled = ref(false);
    const showPassword = ref(false);
    const form = ref({
      username: '',
      password: '',
    });
    const is_check = ref(false);
    const { login } = authStore();


    const onLogin = async () => {
      loading.value = true;
      disabled.value = true;
      try {
        const res = await login(form.value);
        console.log(res);
        if (res.result) {
          loading.value = false;
          disabled.value = false;
        }
        if (res.success) {
          $q.notify({
            color: "green",
            type: 'positive',
            icon: "check",
            position: 'top-right',
            progress: true,
            timeout: 3500,
            message: res.message
          });
          // router.push({name:'dashboard'});
          window.location.href = "/dashboard";
          loading.value = false;
          disabled.value = false;

        } else {
          $q.notify({
            color: "red",
            type: 'negative',
            icon: "error",
            position: 'top-right',
            progress: true,
            timeout: 3500,
            message: res.message
          });
          loading.value = false;
          disabled.value = false;
        }
      } catch (error) {
        loading.value = false;
        disabled.value = false;
        console.log(error);
      }
    };

    return {
      form, showPassword, is_check, onLogin, loading, disabled, logo
    }
  }
});

</script>
<template>
  <q-layout class="bg-primary">
    <q-page-container>
      <q-page class="row justify-center items-center q-col-gutter-sm login">
        <!-- Logo en dehors de la carte -->
        <div class="logo-container">
          <q-avatar size="50px" style="margin-right: 8px;">
            <q-img :src="logo" cover />
          </q-avatar>
        </div>

        <div class="col col-md-4 col-xs-12 q-pa-xl q-mt-md">
          <q-card class="card" flat bordered>
            <div class="text-h5 text-center text-bold q-mt-md">
              <span>Me connecter</span>
            </div>
            <q-form @submit="onLogin">
              <q-card-section class="q-mt-md">
                <q-card-section class="q-pa-sm">
                  <q-input type="text" outlined dense v-model="form.username" label="Nom utilisateur" lazy-rules
                    :rules="[(val: any) => !!val || 'Veuillez entrer votre nom d\'utilisateur']"
                    placeholder="Entrer votre nom d'utilisateur" autocomplete="off">
                    <template v-slot:prepend>
                      <q-icon name="person" />
                    </template>
                  </q-input>
                  <q-input outlined dense :type="showPassword ? 'text' : 'password'" v-model="form.password"
                    label="Mot de Passe" class="q-mt-md" :rules="[(val: any) => !!val || 'Veuillez entrer votre mot de passe']"
                    lazy-rules placeholder="Entrer votre mot de passe" autocomplete="off">
                    <template v-slot:prepend>
                      <q-icon name="lock" />
                    </template>
                    <template v-slot:append>
                      <q-btn color="primary" size="sm" flat :icon="showPassword ? 'visibility_off' : 'visibility'"
                        @click="showPassword = !showPassword" />
                    </template>
                  </q-input>
                </q-card-section>
                <div class="q-mt-md">
                  <q-btn type="submit" color="primary" class="text-white full-width q-mt-md" label="Se connecter"
                    :loading="loading" :disable="disabled">
                    <q-icon name="login" />
                  </q-btn>
                  <q-btn flat class="full-width q-mt-md" color="primary">Mot de passe oublié ?</q-btn>
                </div>
              </q-card-section>
            </q-form>
          </q-card>
        </div>
      </q-page>
    </q-page-container>
  </q-layout>
</template>

<style>
.logo-container {
  position: absolute;
  top: 20px;
  left: 20px;
}

.logo {
  width: 80px;
  /* Ajustez la taille du logo si nécessaire */
}

.card {
  max-width: 360px;
  /* Limite la largeur de la carte */
  margin: auto;
}

.full-width {
  width: 100%;
}

.q-mt-md {
  margin-top: 16px;
}
</style>

