<script lang="ts">
import { defineComponent, ref,PropType,toRefs, onMounted } from 'vue'
import { Chart, Responsive, Pie, Tooltip } from 'vue3-charts'

export const plByMonth = [
  { name: 'Jan', pl: 1000, avg: 500, inc: 300 },
  { name: 'Feb', pl: 2000, avg: 900, inc: 400 },
  { name: 'Apr', pl: 400, avg: 400, inc: 500 },
  { name: 'Mar', pl: 3100, avg: 1300, inc: 700 },
  { name: 'May', pl: 200, avg: 100, inc: 200 },
  { name: 'Jun', pl: 600, avg: 400, inc: 300 },
  { name: 'Jul', pl: 500, avg: 90, inc: 100 },
  
];

interface AgencyData{
  name: string;
  total_cotisation?: number;
  total_carnets?: number;
  wallet_balance?: number;
}

export default defineComponent({
  name: 'LineChart',
  props:{
    agencies: {
      type: Array as PropType<any[]>,
      required: true
    }
  },
  components: { Chart, Responsive, Pie, Tooltip },
  setup(props) {
    const { agencies } = toRefs(props);
    const data = ref<AgencyData[]>([]);

    const axis = ref({
      primary: {
        type: 'band'
      },
      secondary: {
        domain: ['dataMin', 'dataMax + 100'],
        type: 'linear',
        ticks: 9
      }
    }) as any;

    onMounted(() => {
      if (agencies.value !== undefined || agencies.value !== "") {
        console.log('====================================');
        console.log('agencies', agencies.value);
        console.log('====================================');
        
        agencies.value?.forEach(element => {
          data.value.push({
            name: element.name,
            total_cotisation: element.cotisations_sum_total_amount > 0 ? element.cotisations_sum_total_amount : 0,
            total_carnets: element.subscriptions_sum_price > 0 ? element.subscriptions_sum_price : 0,
            wallet_balance: element.wallet_sum_balance > 0 ? element.wallet_sum_balance : 0
          })
        });
        console.log('====================================');
        console.log('data', data.value);
        console.log('====================================');
      }
    });

    return { data, axis ,agencies}
  }
})
</script>

<template>
  <Responsive class="w-full">
    <template #main="{ width }">
      <Chart
        direction="circular"
        :size="{ width: 300, height: 330 }"
        :data="data"
        :margin="{
          left: Math.round((width - 350)/2),
          top: 20,
          right: 0,
          bottom: 20
        }"
        :axis="axis"
        :config="{ controlHover: false }"
        >
        <template #layers>
          <Pie
            
            :dataKeys="['name', 'wallet_balance']"
            :pie-style="{ innerRadius: 100, padAngle: 0.07 }" />
            
        </template>
        <template #widgets>
          <Tooltip
            :config="{
              name: { },
              wallet_balance: { hide: false, label: 'Solde' },
              total_cotisation: { label: 'Total cotisé' },
              total_carnets: { hide: false, label: 'Total carnets' }
            }"
            hideLine
          />
        </template>
      </Chart>
    </template>
  </Responsive>
</template>

