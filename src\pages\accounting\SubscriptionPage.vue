<script lang="ts">
  import { defineComponent, ref } from 'vue';
  import BreadCrumb from 'src/layouts/BreadCrumb.vue';
  import SubscriptionTable from 'src/components/tables/SubscriptionTable.vue';
  export default defineComponent({
    name: "SubscriptionPage",
    components: {
      BreadCrumb,SubscriptionTable
    },
    setup(){

      const bread = ref({
        pageTitle: "Souscriptions",
        subTitle: "Gestion des souscriptions"
      });

      return {
        bread
      };

    }
  })
</script>
<template>
  <q-page class="q-pa-sm">
    <BreadCrumb :bread="bread" />
    <div class="row q-col-gutter-sm">
      <div class="col col-md-12 col-sm-12">
        <q-card class="my-card" flat  >
          <q-toolbar class="text-dark">
            <q-toolbar-title>
              Gestion de carnets
            </q-toolbar-title>
            <!-- <q-btn flat  dense icon="account_circle" label="AJOUTER CAISSIER" class="q-mr-xs" /> -->
          </q-toolbar>
          <SubscriptionTable />
        </q-card>
      </div>
    </div>
  </q-page>
</template>
