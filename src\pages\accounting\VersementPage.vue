<script lang="ts">
  import { defineComponent, ref } from 'vue';
  import BreadCrumb from 'src/layouts/BreadCrumb.vue';
  import VersementTable from 'src/components/tables/VersementTable.vue';
  export default defineComponent({
    name: "VersementPage",
    components: {
      BreadCrumb,VersementTable
    },
    setup(){

      const bread = ref({
        pageTitle: "Versements",
        subTitle: "Gestion des versements"
      });

      return {
        bread
      };

    }
  })
</script>
<template>
  <q-page class="q-pa-md">
    <BreadCrumb :bread="bread" />
    <div class="row q-col-gutter-sm">
      <div class="col col-md-12 col-sm-12">
        <q-card class="my-card" flat >
          <q-toolbar class="">
            <q-toolbar-title>
              Gestion des Versements
            </q-toolbar-title>
          </q-toolbar>
          <VersementTable />
        </q-card>
      </div>
    </div>
  </q-page>
</template>
