import { defineStore } from 'pinia'
import { postData, postDataWithToken,getDataWithToken, getDataWithParams } from '../../helpers/http';
import { Pack, Response } from 'src/models';
import { api } from 'src/router/api';


export const packStore = defineStore('pack', {
  // other options...
  state: () => ({
    loading: false,
    packs: [] as Pack[],
    pack: {} as Pack
  }),
  persist: true,

  getters: {},

  actions: {
    async getPacks(payload?: any): Promise<Response> {
      try {
        const response = await getDataWithParams(api.admin.packs.all,payload);
        if (response.success) {
          this.packs = response.result.data;
        }
        return response;
      } catch (error) {
        console.log(error);
        return {
          success: false,
          message: 'Erreur de connexion veuillez réessayer plus tard',
          result: null,
          errors: null
        };
      }
    },

    async getPackDetail(pack_id: number): Promise<Response> {
      try {
        const response = await postDataWithToken(api.admin.packs.detail, {
          pack_id: pack_id
        });
        if (response.success) {
          this.pack = response.result;
        }
        return response;
      } catch (error) {
        console.log(error);
        return {
          success: false,
          message: "",
          result: null,
          errors: null
        }
      }
    },

    async addPack(payload: any): Promise<Response> {
      try {
        const response = await postDataWithToken(api.admin.packs.add, payload);
        return response;
      } catch (error) {
        console.log(error);
        return {
          success: false,
          message: "",
          result: null,
          errors: null
        }
      }
    },

    async updatePack(payload: any): Promise<Response> {
      try {
        const response = await postDataWithToken(api.admin.packs.update, payload);
        return response;
      } catch (error) {
        console.log(error);
        return {
          success: false,
          message: "",
          result: null,
          errors: null
        }
      }
    },

    async getPackProvisions (pack_id: number): Promise<Response> {
      try {
        const response = await postDataWithToken(api.admin.packs.provisions, {
          pack_id: pack_id
        });
        return response;
      } catch (error) {
        console.log(error);
        return {
          success: false,
          message: "",
          result: null,
          errors: null
        }
      }
    },

    async getPackSubscription(pack_id: number): Promise<Response> {
      try {
        const response = await postDataWithToken(api.admin.packs.add, {
          pack_id: pack_id
        });
        return response;
      } catch (error) {
        console.log(error);
        return {
          success: false,
          message: "",
          result: null,
          errors: null
        }
      }
    },

    async getPackProducts(pack_id: number): Promise<Response> {
      try {
        const response = await getDataWithParams(api.admin.packs.products, {
          pack_id: pack_id
        });
        return response;
      } catch (error) {
        console.log(error);
        return {
          success: false,
          message: "",
          result: null,
          errors: null
        }
      }
    },
  },

});
