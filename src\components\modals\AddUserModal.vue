<script lang="ts">
  import { storeToRefs } from 'pinia';
import { useQuasar } from 'quasar';
import { adminStore } from 'src/stores/core/adminStore';
  import { personalStore } from 'src/stores/core/personalStore';
  import { defineComponent, onMounted, ref } from 'vue';

  export default defineComponent({
    name: "AddUserModal",
    components: {
    },
    setup(){
      const $q = useQuasar();
      const dialog = ref(false);
      const showLoading = ref(false);

      const store = personalStore();
      const {cities,roles} = storeToRefs(adminStore());
      const {addUser,getUsers} = store;

      const form = ref({
        username: "",
        email: "",
        phone: "",
        password: "",
        password_confirmation: "",
        role_id: "" as any,
        status: "",
      });

      const openDialog = async()=>{
        dialog.value = true;
        // await getRoles();
        
        if (roles.value.length >0) {
          roles.value = roles.value.filter((role)=>{
            return (
              role.id !== 3 && role.id !== 4 && role.id !== 5 && role.id !== 6 && role.id !== 7 
            )
          }); 
        }
        console.log('====================================');
        console.log("roles",roles.value);
        console.log('====================================');
      }

      const onSubmit = async()=>{
        console.log(form.value);
        showLoading.value = true;
        try {
          console.log('====================================');
          console.log('form submitted',form.value);
          console.log('====================================');
          const res = await addUser(form.value);
          console.log('=============res=======================');
          console.log("api response",res);
          console.log('====================================');
          if (res.message) {
            showLoading.value = false;
          }
          if (res.success) {
            showLoading.value = false;
            $q.notify({
              color: "green",
              type: 'positive',
              icon: "check",
              position: 'top-right',
              progress: true,
              timeout: 3500,
              message: res.message
            });
            form.value = {
              username: "",
              email: "",
              phone: "",
              password: "",
              password_confirmation: "",
              role_id: "",
              status: "",
            };
            const new_res = await getUsers({page: 1,limit: 25});
            if (new_res.success) {
              dialog.value = false;
            }
          }else{
            $q.notify({
              color: "red-4",
              type: 'negative',
              icon: "error",
              position: 'top-right',
              progress: true,
              timeout: 3500,
              message: res.message
            });
          }

        } catch (error) {
          console.log("Error : ", error );
          showLoading.value = false;
          $q.notify({
            color: "red-4",
            type: 'negative',
            icon: "error",
            position: 'top-right',
            progress: true,
            timeout: 3500,
            message: "Erreur d'ajout, veuillez réessayer svp"
          });
        }
      };

      const onReset = ()=>{

      };

      onMounted(async()=>{
        
      });

      return {
        dialog, form,onSubmit,onReset,roles,showLoading,openDialog
      };

    }
  })

</script>
<template>
  <div class="row">
    <q-btn flat color="" icon="add" label="NOUVEAU COMPTE UTILISATEUR" @click="openDialog" />
    <q-dialog v-model="dialog" persistent full-height transition-show="slide-up" transition-hide="slide-down">
      <q-card>
        <q-form  @submit="onSubmit" @reset="onReset">
          <q-toolbar class="bg-primary text-white">
            <q-toolbar-title>
              Ajouter un utilisateur
            </q-toolbar-title>
            <q-btn flat round dense icon="close" class="q-mr-xs" @click="dialog = false" />
            <q-btn  label="Enregistrer" color="positive" type="submit" />
          </q-toolbar>
          <q-card-section>
            <q-scroll-area style="width: 500px; height: 430px;">
              <div class="row q-col-gutter-md">
                <div class="col col-md-12 col-sm-12 col-xs-12">
                  <q-input filled v-model="form.username" label="Nom d'utilisateur"
                    hint="Nom d'utilisateur" lazy-rules aria-placeholder="Nom d'utilisateur"
                    :rules="[val => !!val || 'Veuillez saisir le nom d\'utilisateur']"
                  />
                </div>
                <div class="col col-md-12 col-sm-12 col-xs-12">
                  <q-input filled v-model="form.email" label="Email"
                    hint="Email" lazy-rules aria-placeholder="Email"
                    :rules="[val => !!val || 'Veuillez saisir l\'email']" type="email"
                  />
                </div>
                <div class="col col-md-12 col-sm-12 col-xs-12">
                  <q-input filled v-model="form.phone" label="Téléphone"
                    hint="Téléphone" lazy-rules aria-placeholder="Téléphone"
                    :rules="[val => !!val || 'Veuillez saisir le téléphone']" mask="(+###) ## ## ## ##"
                  />
                </div>

                <div class="col col-md-12 col-sm-12 col-xs-12">
                  <q-select filled v-model="form.role_id" :options="roles" label="Rôle"
                    hint="Rôle" lazy-rules aria-placeholder="Rôle" option-value="id" option-label="name"
                    :rules="[val => !!val || 'Veuillez choisir le rôle']" emit-value
                  />
                </div>
                <div class="col col-md-12 col-sm-12 col-xs-12">
                  <q-select filled v-model="form.status" :options="['Activé','Inactif']" label="Statut"
                    hint="Statut" lazy-rules aria-placeholder="Statut"
                    :rules="[val => !!val || 'Veuillez choisir le statut']"
                  />
                </div>
                <div class="col col-md-12 col-sm-12 col-xs-12">
                  <q-input filled v-model="form.password" label="Mot de passe"
                    hint="Mot de passe" lazy-rules aria-placeholder="Mot de passe"
                    :rules="[val => !!val || 'Veuillez saisir le mot de passe']" type="password"
                  />
                </div>
                <div class="col col-md-12 col-sm-12 col-xs-12">
                  <q-input filled v-model="form.password_confirmation" label="Confirmer le mot de passe"
                    hint="Confirmer le mot de passe" lazy-rules aria-placeholder="Confirmer le mot de passe"
                    :rules="[val => !!val || 'Veuillez confirmer le mot de passe']" type="password"
                  />
                </div>
              </div>
            </q-scroll-area>
          </q-card-section>
          <q-card-actions align="right">
            <q-btn flat label="Annuler" color="primary" @click="dialog = false" />
            <q-btn  label="Enregistrer" color="positive" type="submit" />
          </q-card-actions>
          <q-inner-loading :showing="showLoading">
            <q-spinner-gears size="50px" color="primary" />
          </q-inner-loading>
        </q-form>
      </q-card>
    </q-dialog>
  </div>
</template>
