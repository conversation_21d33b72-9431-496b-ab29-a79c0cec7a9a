<script lang="ts">
  import { defineComponent, ref } from 'vue';
  import BreadCrumb from 'src/layouts/BreadCrumb.vue';
  import CashierTable from 'src/components/tables/CashierTable.vue';
  export default defineComponent({
    name: "AccountingPage",
    components: {
      BreadCrumb,CashierTable
    },
    setup(){

      const bread = ref({
        pageTitle: "COMPTABILITE",
        subTitle: "ETAT DES CAISSES"
      });

      return {
        bread
      };

    }
  })
</script>
<template>
  <q-page class="q-pa-sm">
    <BreadCrumb :bread="bread" />
    <div class="row q-col-gutter-sm">
      <div class="col col-md-12 col-sm-12">
        <q-card class="my-card" flat >
          <q-toolbar class="">
            <q-toolbar-title>
              Etat des caisses
            </q-toolbar-title>
          </q-toolbar>
          <CashierTable />
        </q-card>
      </div>
    </div>

  </q-page>
</template>
