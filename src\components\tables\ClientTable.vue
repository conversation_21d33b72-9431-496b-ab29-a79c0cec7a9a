<script lang="ts">
import { storeToRefs } from 'pinia';
import { personalStore } from 'src/stores/core/personalStore';
import { defineComponent, onMounted, ref, computed, nextTick } from 'vue';
import EditClientModal from 'src/components/modals/EditClientModal.vue';
import { get_amount_format } from 'src/helpers/utils';
import { adminStore } from 'src/stores/core/adminStore';
import { Agency, Personal } from 'src/models';

export default defineComponent({
  name: "ClientTable",
  components: {
    EditClientModal
  },
  setup() {
    const pagination = ref({
      sortBy: 'desc',
      descending: false,
      page: 1,
      rowsPerPage: 20,
      rowsNumber: 20
    });
    const filter = ref('');
    const loading = ref(false);
    const filterAgency = ref(null);
    const collector = ref(null);
    const collectors = ref<Personal[]>([]);
    const { agencies } = storeToRefs(adminStore());
    const lastPage = Math.ceil(collectors.value.length / 25);
    const nextPage = ref(1);
    const filterDialog = ref(false);
    const typeFilter = ref('') as unknown as 'collector' | 'agency';

    const store = personalStore();
    const { clients } = storeToRefs(personalStore());
    const { getClients, getPersonalByRole } = store;

    const headers = [
      { name: 'nom', label: 'NOM', field: 'nom', sortable: true, align: "left" },
      { name: 'phone', label: 'CONTACT', field: 'phone', sortable: false, align: "left" },
      { name: 'city', label: 'VILLE', field: 'city', sortable: false, align: "left" },
      { name: 'quarter', label: 'QUARTIER', field: 'quarter', sortable: true, align: "left" },
      { name: 'profession', label: 'PROFESSION', field: 'profession', sortable: true, align: "left" },
      { name: 'agency', label: 'AGENCE', field: 'agency', sortable: true, align: "left" },
      { name: 'collector', label: 'COLLECTEUR PRINCIPAL', field: 'collector', sortable: true, align: "left" },
      { name: 'subscriptions', label: 'CARNETS', field: 'subscriptions', sortable: true, align: "left" },
      { name: 'cotisations_sum_total_amount', label: 'COTISATIONS', field: 'cotisations_sum_total_amount', sortable: true, align: "left" },
      { name: 'status', label: 'STATUT', field: 'status', sortable: true, align: "left" },
      { name: 'actions', label: 'Actions', field: 'actions', sortable: false, align: "left" },
    ] as any;

    const onRequest = async (props: any) => {
      loading.value = true;
      pagination.value.page = props.pagination.page;
      pagination.value.rowsPerPage = props.pagination.rowsPerPage;

      const agencyId = filterAgency.value;

      const params = {
        page: pagination.value.page,
        limit: pagination.value.rowsPerPage,
        //@ts-ignore
        ...(filterAgency.value && { agency_id: agencyId })
      };

      const res = await getClients(params);

      if (res.success) {
        loading.value = false;
        pagination.value.rowsNumber = res.result.total;
        pagination.value.page = res.result.current_page;
        clients.value = res.result.data;
      }
    };

    const onScroll = async ({ to, ref }: { to: any, ref: any }) => {
      const lastIndex = collectors.value.length - 1;

      if (loading.value !== true && nextPage.value < lastPage && to === lastIndex) {
        loading.value = true;

        setTimeout(async () => {
          nextPage.value++;
          await nextTick();
          ref.refresh();

          const res = await getPersonalByRole({
            page: nextPage.value,
            limit: pagination.value.rowsPerPage,
            role_id: 6
          });

          if (res.success) {
            collectors.value = [...collectors.value, ...res.result.data];
            pagination.value.rowsNumber = res.result.total;
            pagination.value.page = res.result.current_page;
          }

          loading.value = false;
        }, 500);
      }
    };

    const onFilterByAgency = async () => {
      loading.value = true;
      console.log("agency", filterAgency.value);

      if (filterAgency.value == null) {
        return clients.value;
      }

      const res = await getClients({
        limit: 20,
        page: 1,
        agency_id: filterAgency.value
      });
      if (res.success) {
        loading.value = false;
        pagination.value.rowsNumber = res.result.total;
        pagination.value.page = res.result.current_page;
        clients.value = res.result.data;
      }
    }

    onMounted(async () => {
      loading.value = true;
      const res = await getClients({
        limit: 15,
        page: 1
      });
      if (res.success) {
        loading.value = false;
        pagination.value.rowsNumber = res.result.total;
        pagination.value.page = res.result.current_page;
        clients.value = res.result.data;
      } else {
        loading.value = false;
      }
    });

    return {
      pagination, filter, headers, clients, agencies, filterAgency, loading, filterDialog, typeFilter,
      get_amount_format, onFilterByAgency, onRequest, collector, collectors, onScroll
    };
  }
});
</script>


<template>
  <div class="q-pa-xs">

    <q-table flat bordered title="Liste des clients" :rows="clients" :columns="headers" row-key="name"
      v-model:pagination="pagination" :filter="filter" table-style="max-width: 100%;" :loading="loading"
      table-class="my-sticky-header-table" @request="onRequest">
      <template v-slot:top-right="props">
        <q-btn flat round dense :icon="props.inFullscreen ? 'fullscreen_exit' : 'fullscreen'"
          @click="props.toggleFullscreen" class="q-ml-md" />
        <q-input filled dense debounce="300" v-model="filter" clearable clear-icon="close"
          placeholder="Rechercher un client">
          <template v-slot:append>
            <q-icon name="search" />
          </template>
        </q-input>
        <q-btn color="primary" icon="filter_alt" label="Filtrer" @click="filterDialog = true" />

      </template>

      <template v-slot:no-data="{ icon, message }">
        <div class="full-width row flex-center text-accent q-gutter-sm">
          <q-icon size="2em" name="sentiment_dissatisfied" />
          <span>
            Aucun client trouvé
          </span>
          <q-icon size="2em" :name="filter ? 'filter_b_and_w' : icon" />
        </div>
      </template>
      <template v-slot:body="props">
        <q-tr :props="props">
          <q-td key="nom" :props="props">
            <router-link :to="{ name: 'detail-client', params: { code: props.row.code } }">
              {{ props.row.nom }} {{ props.row.prenoms }}
            </router-link>
          </q-td>
          <q-td key="prenoms" :props="props">
            {{ props.row.prenoms }}
          </q-td>
          <q-td key="phone" :props="props">
            {{ props.row.phone }}
          </q-td>
          <q-td key="city" :props="props">
            {{ props.row.city?.name }}
          </q-td>
          <q-td key="quarter" :props="props">
            {{ props.row.quarter?.name }}
          </q-td>
          <q-td key="profession" :props="props">
            {{ props.row.profession }}
          </q-td>
          <q-td key="agency" :props="props">
            {{ props.row.agency?.name }}
          </q-td>
          <q-td key="collector" :props="props">
            {{ props.row?.collector[0]?.nom }} {{ props.row?.collector[0]?.prenoms }}
          </q-td>
          <q-td key="subscriptions" :props="props">
            {{ props.row?.subscriptions_count }}
          </q-td>
          <q-td key="cotisations_sum_total_amount" :props="props">
            {{ get_amount_format(props.row?.cotisations_sum_total_amount) }} {{ props.row?.cotisations_sum_total_amount
              !== null ? 'FCFA' : '' }}
          </q-td>
          <q-td key="status" :props="props">
            <q-chip size="sm" class="text-dark" outline
              :color="props.row.status === 'active' ? 'positive' : 'negative'">
              {{ props.row.status === 'active' ? "ACTIVE" : "INACTIVE" }}
            </q-chip>
          </q-td>
          <q-td key="actions" :props="props" style="display: flex">
            <q-btn dense flat color="primary" icon="visibility" class="q-mr-xs"
              :to="{ name: 'detail-client', params: { code: props.row.code } }" />
            <EditClientModal :client="props.row" />
          </q-td>
        </q-tr>
      </template>
    </q-table>
  </div>
  <q-dialog v-model="filterDialog" persistent>
    <q-card flat class="" style="width: 500px">
      <q-card-section>
        <div class="text-h6">Filtrer par {{ typeFilter === 'collector' ? 'Collecteur' : 'Agence' }}</div>
      </q-card-section>

      <q-card-section class="">
        <div class="row q-col-gutter-sm ">
          <div class="col col-md-12">
            <q-radio v-model="typeFilter" checked-icon="task_alt" unchecked-icon="panorama_fish_eye" val="collector"
              label="Agent Collecteur" />
            <q-radio v-model="typeFilter" checked-icon="task_alt" unchecked-icon="panorama_fish_eye" val="agency"
              label="Agences" />
          </div>
          <div class="col col-md-12" v-if="typeFilter === 'agency'">
            <q-select v-model="filterAgency" :options="agencies" label="Agence" filled dense class=""
              @update:model-value="onFilterByAgency" option-label="name" option-value="id" emit-value map-options
              behavior="dialog" clearable />
          </div>
          <div class="col col-md-12" v-if="typeFilter === 'collector'">
            <q-select filled dense label="Agent Collecteur" v-model="collector" option-label="nom" option-value="id"
              :options="collectors" behavior="dialog" clearable />
          </div>
        </div>
      </q-card-section>

      <q-card-actions align="right" class="text-primary">
        <q-btn flat label="Annuler" color="primary" v-close-popup />
        <q-btn flat label="Appliquer" color="primary" v-close-popup />
      </q-card-actions>
    </q-card>
  </q-dialog>
</template>
