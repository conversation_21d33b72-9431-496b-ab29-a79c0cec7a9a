<script lang="ts">
import { storeToRefs } from 'pinia';
import { adminStore } from 'src/stores/core/adminStore';
import { defineComponent, onMounted, ref } from 'vue';

export default defineComponent({
  name: "CityTable",
  setup() {
    const initialPagination = ref({
      sortBy: 'name',
      descending: false,
      page: 1,
      rowsPerPage: 10
    });
    const filter = ref('');
    const loading = ref(false);

    const store = adminStore();
    const { cities } = storeToRefs(adminStore());
    const { getCountries, getCities } = store;

    const headers = [
      { name: 'id', label: 'ID', field: 'id', sortable: true, hide: true, align: "left" },
      { name: 'name', label: 'NOM', field: 'name', sortable: true, align: "left" },
      { name: 'country', label: 'PAYS ', field: 'country', sortable: true, align: "left" },
      { name: 'quarters', label: 'QUARTIERS', field: 'quarters', sortable: true, align: "left" },
      { name: 'agencies', label: 'AGENCES', field: 'agencies', sortable: true, align: "left" },
      { name: 'personals', label: 'PERSONELS', field: 'personals', sortable: true, align: "left" },
      { name: 'clients', label: 'CLIENTS', field: 'clients', sortable: true, align: "left" },
      // { name: 'actions', label: 'ACTIONS', field: 'actions', sortable: false },
    ] as any;
    const columns_visibles = ['name', 'country', 'actions', 'quarters', 'agencies', 'personals', 'clients'];

    onMounted(async () => {
      loading.value = true;
      await getCities();
      if (cities.value.length > 0) {
        loading.value = false
      } else {
        setTimeout(() => {
          loading.value = false
        }, 2500);
      }
    });

    return {
      initialPagination, filter, headers, cities, columns_visibles
    };

  }
});
</script>

<template>
  <div class="">
    <q-table flat bordered title="Liste des villes" :rows="cities" :columns="headers" row-key="name"
      :pagination="initialPagination" :filter="filter" table-style="max-width: 100%;"
      :visible-columns="columns_visibles">
      <template v-slot:top-right>
        <q-input filled dense debounce="300" v-model="filter" clearable clear-icon="close"
          placeholder="Rechercher une ville">
          <template v-slot:append>
            <q-icon name="search" />
          </template>
        </q-input>
      </template>

      <template v-slot:no-data="{ icon, message, }">
        <div class="full-width row flex-center text-accent q-gutter-sm">
          <q-icon size="2em" name="sentiment_dissatisfied" />
          <span>
            Aucune catégorie trouvée
          </span>
          <q-icon size="2em" :name="filter ? 'filter_b_and_w' : icon" />
        </div>
      </template>
      <template v-slot:body="props">
        <q-tr :props="props">
          <q-td key="name" :props="props">
            {{ props.row.name }}
          </q-td>
          <q-td key="country" :props="props">
            {{ props.row.country?.name }}
          </q-td>
          <q-td key="quarters" :props="props">
            {{ props.row.quarters_count }}
          </q-td>
          <q-td key="agencies" :props="props">
            {{ props.row.agencies_count }}
          </q-td>
          <q-td key="personals" :props="props">
            {{ props.row.personals_count }}
          </q-td>
          <q-td key="clients" :props="props">
            {{ props.row.clients_count }}
          </q-td>
          <q-td key="actions" :props="props">
            <q-btn dense flat color="primary" icon="visibility" class="q-mr-xs" />
            <q-btn dense flat icon="edit" color="secondary" class="q-mr-xs" />
          </q-td>
        </q-tr>
      </template>
    </q-table>
  </div>
</template>
