import { defineStore } from 'pinia'
import { postData, postDataWithToken } from '../../helpers/http';
import { getCookie, deleteCookie, convertToTime } from '../../helpers/myfunc';
import type { User } from '../../models';
import type {Response} from '../../models';

// @ts-ignore
let userData = JSON.parse(localStorage.getItem('user')) as User;
// @ts-ignore
let token = getCookie('token');

export const authStore = defineStore('auth', {
  // other options...
  state: () => ({
    user: userData || {} as User,
    loading: false,
    error: null,
    token: token || null,
  }),
  getters: {
    getUser: (state) => {
      return state.user;
    },
    getToken: (state) => {
      return state.token;
    },


  },
  actions: {

    async login(payload: any): Promise<Response> {
      try {
        const response = await postData('auth/login', payload);
        if (response.success) {
          const result = response.result;
          const role_id = result.user.role_id;
          if (role_id === 1 || role_id === 2) {
            this.user = result.user;
            this.token = result.token;
            localStorage.setItem('user', JSON.stringify(this.user));
            localStorage.setItem('token', JSON.stringify(this.token));
            console.log(this.token);
            const expirationTime = new Date(Date.now() + 8 * 60 * 60 * 1000).toUTCString();
            const tokenExpiration  = convertToTime(expirationTime) as any;
            localStorage.setItem('tokenExpiration',tokenExpiration);
            // document.cookie = `token=${this.token}; expires=${expirationTime}`;
            return response;
          } else {
            return {
              success: false,
              message: "Vous n'avez pas le privilège de vous connecter",
              result: null,
              errors: null
            };

          }
        } else {
          return {
            success: false,
            message: response.message,
            result: null,
            errors: response.errors
          };
        }
      } catch (error: any) {
        console.log(error);
        return {
          success: false,
          message: 'Erreur de connexion veuillez réessayer plus tard',
          result: null,
          errors: "Erreur de connexion"
        };
      }
    },

    async register_partner(payload: any): Promise<Response> {
      try {
        const response = await postData('auth/partner/register', payload);
        //this.user = response.result;
        return response;

      } catch (error: any) {
        return error;
      }
    },

    async verify_code(payload: any): Promise<Response> {
      const response = await postData('auth/partner/verify-account', payload);
      //this.user = response.result;
      return response;
    },

    async logout(): Promise<Response> {
      const response = await postDataWithToken('auth/logout');
      if (response.success) {
        this.user = {
          id: 0,
          username: '',
          email: '',
          role_id: 0,
          password: '',
        };
        localStorage.removeItem('token');
        localStorage.removeItem('user');
        localStorage.removeItem('tokenExpiration');
        // deleteCookie('token');

        return response;
      } else {
        return {
          success: false,
          message: 'Erreur de connexion veuillez réessayer plus tard',
          result: null,
        }
      }
    },

    async register_user(payload: any): Promise<Response> {
      try {
        const response = await postData('auth/user/register', payload);
        //this.user = response.result;
        return response;

      } catch (error: any) {
        return error;
      }
    }


  }
});
