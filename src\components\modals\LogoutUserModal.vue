<script lang="ts">
import { useQuasar } from 'quasar';
import { personalStore } from 'src/stores/core/personalStore';
import { defineComponent,ref, toRefs } from 'vue';

export default defineComponent({
  name: 'LogoutUserModal',
  props: {
    user: {
      type: Object,
    }
  },
  setup(props) {

    const dialog = ref(false);
    const showLoading = ref(false);
    const $q = useQuasar();

    const {user} = toRefs(props);
    const store = personalStore();
    const {disconnectUser} = store;

    const form = ref({
      user_id: "",
      secret_code: "",
    });

    const openDialog = () => {
      if (user.value  !== undefined || user.value !== "") {
        dialog.value = true;
        form.value.user_id = user.value?.id;
      }
    }

    const onLogout = async()=>{
      showLoading.value = true;
      try {
        console.log("form submitted",form.value);
        const res = await disconnectUser(form.value);
        if (res.message) {
          showLoading.value = false;
          dialog.value = false;
        }

        if (res.success) {
          $q.notify({
            color: "green",
            type: 'positive',
            icon: "check",
            position: 'top-right',
            progress: true,
            timeout: 3500,
            message: "User disconnected successfully"
          });
        } else {
          $q.notify({
            color: "red-4",
            type: 'negative',
            icon: "error",
            position: 'top-right',
            progress: true,
            timeout: 3500,
            message: res.message
          });
        }
      } catch (error) {
        console.log("Error : ", error );
        showLoading.value = false;
        dialog.value = false;
        $q.notify({
          color: "red-4",
          type: 'negative',
          icon: "error",
          position: 'top-right',
          progress: true,
          timeout: 3500,
          message: "Echec de déconnexion de l'utilisateur",
        });
      }
    }

    return {
      dialog,showLoading,onLogout,user,form,openDialog
    };
  }
})
</script>
<template>
  <div class="">
    <q-btn dense flat  color="negative" icon="logout" title="Déconnecter" @click="openDialog"/>
    <q-dialog v-model="dialog" persistent>
      <q-card>
        <q-form @submit="onLogout">
          <q-card-section class="row items-center">
            <q-avatar icon="logout" color="negative" text-color="white" />
            <span class="q-ml-sm">
              Voulez-vous vraiment vous déconnecter l'utilisateur {{ user?.username }} ?
            </span>
          </q-card-section>
          <q-card-section>
            <div class="col col-md-12 col-sm-12 col-xs-12">
              <q-input v-model="form.secret_code" type="text" label="Code Secret" placeholder="Entrez votre code secret" />
            </div>
          </q-card-section>
          <q-card-actions align="right">
            <q-btn flat label="ANNULER" color="primary" v-close-popup />
            <q-btn flat label="DECONNECTER" color="primary" type="submit" />
          </q-card-actions>
        </q-form>
        <q-inner-loading :showing="showLoading">
          <q-spinner-gears size="50px" color="primary" />
        </q-inner-loading>
      </q-card>
    </q-dialog>
  </div>
</template>
