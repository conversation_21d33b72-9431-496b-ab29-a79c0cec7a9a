<script lang="ts">
import { storeToRefs } from 'pinia';
import { productStore } from 'src/stores/core/productStore';
import { defineComponent, onMounted, ref } from 'vue';
import { get_date_format, get_amount_format } from 'src/helpers/utils';
import EditProductModal from 'components/modals/EditProductModal.vue';

export default defineComponent({
  name: "ProductTable",
  components: {
    EditProductModal
  },
  setup() {
    const pagination = ref({
      sortBy: 'desc',
      descending: false,
      page: 1,
      rowsPerPage: 20,
      rowsNumber: 20
    });
    const filter = ref('');
    const loading = ref(false);

    const store = productStore();
    const { products } = storeToRefs(productStore());
    const { getProducts } = store;

    const headers = [
      { name: 'name', label: 'LIBELLE', field: 'name', sortable: true, align: "left" },
      { name: 'category', label: 'CATEGORIE', field: 'category', sortable: true, align: "left" },
      { name: 'price_achat', label: "PRIX D'ACHAT", field: 'price_achat', sortable: true, align: "left" },
      { name: 'price_vente', label: "PRIX DE VENTE", field: 'price_vente', sortable: true, align: "left" },
      { name: 'stock_quantity', label: 'QUANTITE STOCK ', field: 'stock_quantity', sortable: true, align: "left" },
      { name: 'created_at', label: 'DATE CREATION ', field: 'created_at', sortable: true, align: "left" },
      { name: 'actions', label: 'ACTIONS', field: 'actions', sortable: false },
    ] as any;

    const onRequest = async (props: any) => {
      loading.value = true;
      pagination.value.page = props.pagination.page;
      pagination.value.rowsPerPage = props.pagination.rowsPerPage;

      const res = await getProducts({
        page: pagination.value.page,
        limit: pagination.value.rowsPerPage,
      });

      if (res.success) {
        loading.value = false;
        pagination.value.rowsNumber = res.result.total;
        pagination.value.page = res.result.current_page;
        products.value = res.result.data;
      }
    };

    onMounted(async () => {
      loading.value = true;
      const res = await getProducts({
        limit: 20,
        page: 1
      });
      if (res.success) {
        loading.value = false;
        pagination.value.rowsNumber = res.result.total;
        pagination.value.page = res.result.current_page;
        products.value = res.result.data;
      } else {
        loading.value = false;
      }
    });

    return {
      pagination, filter, headers, products, get_amount_format, get_date_format,onRequest,loading
    };

  }
});
</script>

<template>
  <div class="q-pa-xs">
    <q-table flat bordered title="Liste des produits" :rows="products" :columns="headers" row-key="name"
    v-model:pagination="pagination" :filter="filter" table-style="max-width: 100%;" :loading="loading"
    table-class="my-sticky-header-table" @request="onRequest" >
      <template v-slot:top-right>
        <q-input filled dense debounce="300" v-model="filter" clearable clear-icon="close"
          placeholder="Rechercher une catégorie">
          <template v-slot:append>
            <q-icon name="search" />
          </template>
        </q-input>
      </template>

      <template v-slot:no-data="{ icon, message, }">
        <div class="full-width row flex-center text-accent q-gutter-sm">
          <q-icon size="2em" name="sentiment_dissatisfied" />
          <span>
            Aucune catégorie trouvée
          </span>
          <q-icon size="2em" :name="filter ? 'filter_b_and_w' : icon" />
        </div>
      </template>
      <template v-slot:body="props">
        <q-tr :props="props">
          <q-td key="name" :props="props">
            {{ props.row.name }}
          </q-td>
          <q-td key="category" :props="props">
            {{ props.row.category?.name }}
          </q-td>
          <q-td key="price_achat" :props="props">
            {{ get_amount_format(props.row.price_achat) }}
          </q-td>
          <q-td key="price_vente" :props="props">
            {{ get_amount_format(props.row.price_vente) }}
          </q-td>
          <q-td key="stock_quantity" :props="props">
            {{ props.row.stock_quantity }}
          </q-td>
          <q-td key="created_at" :props="props">
            {{ get_date_format(props.row.created_at) }}
          </q-td>

          <q-td key="actions" :props="props" style="display: flex;">
            <EditProductModal :product="props.row" />
            <q-btn dense flat color="positive"  label="Details" class="q-mr-xs" />
          </q-td>
        </q-tr>
      </template>
    </q-table>
  </div>
</template>
