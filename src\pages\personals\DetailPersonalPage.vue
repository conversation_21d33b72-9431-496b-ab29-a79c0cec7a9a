<script lang="ts">
  import { defineComponent, ref, toRefs,onMounted } from 'vue';
  import BreadCrump from 'layouts/BreadCrumb.vue';
  import { useQuasar } from 'quasar';
  import { adminStore } from 'src/stores/core/adminStore';
  import { personalStore } from 'src/stores/core/personalStore';
  import { useRoute } from 'vue-router';
  import {get_phone_format,get_date_format,get_amount_format} from 'src/helpers/utils';
  import { storeToRefs } from 'pinia';
  import EditPersonalModal from 'components/modals/EditPersonalModal.vue';
  import CollectorClientTable from './collectors/CollectorClientTable.vue';
  import CollectorCarnets from './collectors/CollectorCarnets.vue';
  import CollectorCotisations from './collectors/CollectorCotisations.vue';
  import CollectorVersementTable from './collectors/CollectorVersementTable.vue';

  export default defineComponent({
    name: 'DetailPersonalPage',
    components: {
      BreadCrump,EditPersonalModal,CollectorClientTable,CollectorCarnets,CollectorCotisations,
      CollectorVersementTable
    },
    setup() {
      const $q = useQuasar();
      const route = useRoute();
      const expand = ref(false);
      const tab = ref('infos');
      const analytic_tab = ref('clients');

      const personal_id= Number(route.params.id);
      const store = personalStore();
      const {personal} = storeToRefs(personalStore());
      const { get_personal,getDetailPersonal,getPersonalCode } = store;
      const personalInfo = get_personal(personal_id);
      const current_agency = personalInfo?.current_agency?.[0];
      const total_price_to_collect = ref(0);
      const total_price_rest = ref(0);
      const clients = ref([]) as any;
      const subscriptions = ref([]) as any;
      const cotisations = ref([]) as any;
      const versements = ref([]) as any;

      const bread = ref({
        pageTitle: 'GRH',
        subTitle: personalInfo?.nom
      });
      const dialog = ref(false);
      const loading = ref(false);
      const code_message = ref("");
      const form_showCode = ref({
        secret_code: "",
        user_id: 0 as any,
      });

      const showSecret = async()=>{
        console.log("secret code", form_showCode.value);
        loading.value = true;
        try {
          const res = await getPersonalCode(form_showCode.value);
          if (res.message) {
            loading.value = false;
          }
          if (res.success) {
            form_showCode.value = {
              secret_code: "",
              user_id: 0 as any
            };
            code_message.value = res.message;
            dialog.value = true;
            loading.value = false;
          } else {
            $q.notify({
              color: "red-4",
              type: 'negative',
              icon: "error",
              position: 'top-right',
              progress: true,
              timeout: 3500,
              message: res.message
            });
            loading.value = false;
          }
        } catch (error) {
          console.log("error",error);

          $q.notify({
            color: "red-4",
            type: 'negative',
            icon: "error",
            position: 'top-right',
            progress: true,
            timeout: 3500,
            message: "Echec, une erreur est survenue, veuillez réessayez plus tard svp"
          });
          loading.value = false;
        }

      }

      onMounted(async () => {
        if (personal_id !== undefined || personal_id !== "") {
          await getDetailPersonal(personal_id);
          console.log("personal info", personalInfo);
          console.log('====================================');
          console.log('personalInfo', personal.value);
          console.log('====================================');
          bread.value.subTitle = personalInfo?.nom;
          form_showCode.value.user_id = personal.value.user?.id;

          clients.value = personal.value?.clients;
          subscriptions.value = personal.value?.subscriptions;
          cotisations.value = personal.value?.cotisations;
          versements.value = personal.value?.versements;

          if (subscriptions) {
            // console.log("subscriptions",subscriptions.value);

            subscriptions.value.forEach((element: any) => {
              const pack = element?.pack;

              if (pack) {
                console.log('====================================');
                console.log('pack', pack.total_price);
                console.log('====================================');
                let total_price = Number(pack.total_price);
                total_price_to_collect.value += total_price;
              }
            });
            // console.log("Total price to collect",total_price_to_collect);


          }
          if (cotisations) {
            let cotisations_sum_total_amount = Number(personal.value?.cotisations_sum_total_amount);
            total_price_rest.value = total_price_to_collect.value - cotisations_sum_total_amount;
          }
        }
      });

      return {
        bread,personalInfo,expand,tab,get_phone_format,current_agency,
        get_date_format,personal,get_amount_format,total_price_to_collect,
        total_price_rest,analytic_tab,clients,subscriptions,cotisations,versements,
        form_showCode,showSecret,loading,dialog,code_message
      };
    }
  });

</script>
<template>
  <q-page class="q-pa-md">
    <BreadCrump :bread="bread" />
    <div class="row q-col-gutter-sm">
      <div class="col col-md-4 col-sm-12 col-xs-12">
        <q-card class="my-card">
          <q-card-section>
            <div style="padding-left: 35%;" class="row items-center">
              <q-avatar size="100px" font-size="52px" color="teal" text-color="white" icon="person" class="q-mb-sm text-center" />
            </div>
            <div class="text-center " style="font-weight: bold; font-size: large;">{{ personalInfo?.nom }} {{ personalInfo?.prenoms }} </div>
            <div style="padding-bottom: 50px;" >
              <q-input :model-value="personalInfo?.role?.name" type="text" label="Role" filled readonly />
              <q-input :model-value="get_phone_format(personalInfo?.phone)" type="text" label="Téléphone" filled readonly/>
              <q-input :model-value="personalInfo?.email" type="text" label="Email" filled readonly/>
              <q-input :model-value="personalInfo?.city?.name" type="text" label="Ville" filled readonly />
              <q-input :model-value="personalInfo?.quarter?.name" type="text" label="Quartier" filled readonly />
            </div>
          </q-card-section>
        </q-card>
      </div>
      <div class="col col-md-8 col-sm-12 col-xs-12">
        <q-card class="my-card">
          <q-card-section>
            <q-tabs v-model="tab" class="text-teal" align="justify" inline-label  >
              <q-tab name="analytics" icon="analytics" label="STATISTIQUES" v-show="personalInfo?.role_id == 6" />
              <q-tab name="infos" icon="account_circle" label="INFORMATIONS PERSONNELLE" />
              <q-tab name="account" icon="infos" label="INFORMATION PROFESSIONNELLE" />
              <!-- <q-tab name="settings" icon="settings" label="PARAMETRES" /> -->
              <q-tab name="security" icon="security" label="SECURITE" />
            </q-tabs>
          </q-card-section>
          <q-card-section>
            <q-tab-panels v-model="tab" animated>
              <q-tab-panel name="infos">
                <!-- <div class="text-h6">Details des informations personnelles</div> -->

                <EditPersonalModal :personal="personal" />
                <div class="row q-col-gutter-sm">
                  <div class="col col-md-6 col-sm-12 col-xs-12">
                    <q-input filled :model-value="personalInfo?.nom" label="Nom" readonly />
                  </div>
                  <div class="col col-md-6 col-sm-12 col-xs-12">
                    <q-input filled :model-value="personalInfo?.prenoms" label="Prénoms" readonly />
                  </div>
                  <div class="col col-md-6 col-sm-12 col-xs-12">
                    <q-input filled :model-value="personalInfo?.phone" label="Téléphone" readonly />
                  </div>
                  <div class="col col-md-6 col-sm-12 col-xs-12">
                    <q-input filled :model-value="personalInfo?.email" label="Email" readonly />
                  </div>
                  <div class="col col-md-6 col-sm-12 col-xs-12">
                    <q-input filled :model-value="personalInfo?.date_nsce" label="Date de Naissance" readonly />
                  </div>
                  <div class="col col-md-6 col-sm-12 col-xs-12">
                    <q-input filled :model-value="personalInfo?.lieu_nsce" label="Lieu de Naissance" readonly />
                  </div>
                  <div class="col col-md-6 col-sm-12 col-xs-12">
                    <q-input filled :model-value="personalInfo?.gender" label="Civilité" readonly />
                  </div>
                  <div class="col col-md-6 col-sm-12 col-xs-12">
                    <q-input filled :model-value="personalInfo?.city?.name" label="Ville de résidence" readonly />
                  </div>
                  <div class="col col-md-6 col-sm-12 col-xs-12">
                    <q-input filled :model-value="personalInfo?.quarter?.name" label="Quartier" readonly />
                  </div>
                  <div class="col col-md-6 col-sm-12 col-xs-12">
                    <q-input filled :model-value="personalInfo?.situation_matrimoniale" label="Situation matrimoniale" readonly />
                  </div>

                </div>
              </q-tab-panel>
              <q-tab-panel name="account">
                <div class="text-h6">Données professionnelles</div>
                <div class="row q-col-gutter-sm">
                  <div class="col col-md-12 col-sm-12 col-xs-12">
                    <!-- <q-item-label>Status</q-item-label> -->
                    <q-chip class="bg-primary text-white" :color="personalInfo?.status == '1' ? 'positive' : 'negative'" >
                      {{ personalInfo?.status == '1' ? 'Actif' : 'Inactif' }}
                    </q-chip>
                  </div>
                  <div class="col col-md-12 col-sm-12 col-xs-12">
                    <q-input filled :model-value="personalInfo?.role?.name" label="Fonction" readonly />
                  </div>
                  <div class="col col-md-12 col-sm-12 col-xs-12">
                    <q-input filled :model-value="current_agency?.name" label="Agence actuelle" readonly />
                  </div>
                  <div class="col col-md-12 col-sm-12 col-xs-12">
                    <q-input filled :model-value="get_date_format(personalInfo?.created_at)" label="Date création" readonly />
                  </div>
                </div>
              </q-tab-panel>
              <q-tab-panel name="settings">
                <!-- <div class="text-h6">Configuration du personnel</div> -->
              </q-tab-panel>

              <q-tab-panel name="analytics">
                <div class="text-h6">Statistiques du collecteur</div>
                <div class="row q-col-gutter-sm">
                  <div class="col col-md-4 col-xs-6">
                    <q-card class="my-card analytic">
                      <q-card-section>
                        <div class="text-h6 text-center">Total Client</div>
                        <div class="text-subtitle2"></div>
                      </q-card-section>
                      <q-card-section>
                        <div class="text-center text-h5">
                          {{ personal?.clients?.length }}
                        </div>
                      </q-card-section>
                    </q-card>
                  </div>
                  <div class="col col-md-4 col-xs-6">
                    <q-card class="my-card analytic">
                      <q-card-section>
                        <div class="text-h6 text-center">Carnets Vendus</div>
                        <div class="text-subtitle2"></div>
                      </q-card-section>
                      <q-card-section>
                        <div class="text-center text-h4">
                          {{ personal?.subscriptions?.length }}
                        </div>
                      </q-card-section>
                    </q-card>
                  </div>
                  <div class="col col-md-4 col-xs-12">
                    <q-card class="my-card analytic">
                      <q-card-section>
                        <div class="text-h6 text-center">Frais carnets</div>
                        <div class="text-subtitle2"></div>
                      </q-card-section>
                      <q-card-section>
                        <div class="text-center text-h5">
                          {{ get_amount_format(personal?.subscriptions_sum_price) }}
                        </div>
                      </q-card-section>
                    </q-card>
                  </div>
                  <div class="col col-md-5 col-xs-12">
                    <q-card class="my-card analytic">
                      <q-card-section>
                        <div class="text-h6 text-center">Total collecté</div>
                        <div class="text-subtitle2"></div>
                      </q-card-section>
                      <q-card-section>
                        <div class="text-center text-h5">
                          {{ get_amount_format(personal?.cotisations_sum_total_amount) }}
                        </div>
                      </q-card-section>
                    </q-card>
                  </div>
                  <div class="col col-md-7 col-sm-12 col-xs-12">
                    <q-card class="my-card analytic">
                      <q-card-section>
                        <div class="text-h6 text-center">Total à collecter</div>
                        <div class="text-subtitle2"></div>
                      </q-card-section>
                      <q-card-section>
                        <div class="text-center text-h5">
                          {{ get_amount_format(total_price_to_collect) }}
                        </div>
                      </q-card-section>
                    </q-card>
                  </div>
                  <div class="col col-md-6 col-sm-12 col-xs-12">
                    <q-card class="my-card analytic">
                      <q-card-section>
                        <div class="text-h6 text-center">Reste à collecter</div>
                        <div class="text-subtitle2"></div>
                      </q-card-section>
                      <q-card-section>
                        <div class="text-center text-h5">
                          {{ get_amount_format(total_price_rest) }}
                        </div>
                      </q-card-section>
                    </q-card>
                  </div>
                  <div class="col col-md-6 col-sm-12 col-xs-12">
                    <q-card class="my-card analytic">
                      <q-card-section>
                        <div class="text-h6 text-center">Total versé</div>
                        <div class="text-subtitle2"></div>
                      </q-card-section>
                      <q-card-section>
                        <div class="text-center text-h5">
                          {{ get_amount_format(personal?.versements_sum_amount) }}
                        </div>
                      </q-card-section>
                    </q-card>
                  </div>
                </div>
              </q-tab-panel>

              <q-tab-panel name="security">
                <div class="text-h6">Paramètres de securité du personnel</div>
                <q-form  @submit="showSecret"  >
                  <div class="row q-col-gutter-sm">
                    <div class="col col-md-8 col-sm-12">
                      <q-input v-model="form_showCode.secret_code" type="text" label="Code secret" placeholder="Entrez votre code secret"
                      lazy-rules :rules="[ val => val && val.length > 0 || 'Entrez votre code secret']"
                      />
                    </div>
                    <div class="col col-md-4 col-sm-12">
                      <q-btn size="md" color="primary" icon="lock" label="AFFICHER CODE" type="submit" :loading="loading" />
                    </div>
                  </div>
                </q-form>
              </q-tab-panel>
            </q-tab-panels>
          </q-card-section>
        </q-card>
      </div>
    </div>

    <div class="row q-col-gutter-sm q-pt-md">
      <div class="col col-md-12 col-sm-12 col-xs-12">
        <q-card class="my-card">
          <q-toolbar class="bg-primary text-white">
            <q-btn flat round dense icon="analytics" />
            <q-toolbar-title>
              Tableau de bord
            </q-toolbar-title>
            <q-btn flat round dense icon="refresh" class="q-mr-xs" />
            <q-btn flat round dense icon="more_vert" />
          </q-toolbar>
          <q-card-section>
            <q-tabs v-model="analytic_tab" class="text-teal" align="justify" inline-label >
              <q-tab name="clients" icon="groups" label="CLIENTS" />
              <q-tab name="carnets" icon="credit_card" label="CARNETS" />
              <q-tab name="cotisations" icon="account_balance" label="COTISATIONS" />
              <q-tab name="versements" icon="account_balance" label="VERSEMENTS" />
              <q-tab name="controls" icon="insert_chart" label="CONTROLES" />
            </q-tabs>
          </q-card-section>
          <q-card-section>
            <q-tab-panels v-model="analytic_tab" animated>
              <q-tab-panel name="clients">
                <!-- <div class="text-h6">Clients</div> -->
                <CollectorClientTable :clients="clients" />
              </q-tab-panel>
              <q-tab-panel name="carnets">
                <!-- <div class="text-h6">Carnets</div> -->
                <CollectorCarnets :subscriptions="subscriptions" />
              </q-tab-panel>
              <q-tab-panel name="cotisations">
                <!-- <div class="text-h6">Cotisations</div> -->
                <CollectorCotisations :cotisations="cotisations" />
              </q-tab-panel>
              <q-tab-panel name="versements">
                <!-- <div class="text-h6">Cotisations</div> -->
                <CollectorVersementTable :versements="versements" />
              </q-tab-panel>
            </q-tab-panels>
          </q-card-section>
        </q-card>
      </div>
    </div>

    <div class="row q-col-gutter-sm">
      <q-dialog v-model="dialog" persistent>
        <q-card>
          <q-card-section class="row items-center">
            <q-avatar icon="lock" color="primary" text-color="white" />
            <span class="q-ml-sm">
              {{ code_message }}
            </span>
          </q-card-section>
          <q-card-actions align="right">
            <q-btn flat label="FERMER" color="primary" v-close-popup />
          </q-card-actions>
        </q-card>
      </q-dialog>
    </div>

  </q-page>
</template>

<style scoped>
  .analytic {
    background: -moz-linear-gradient(0deg, rgba(173, 216, 230, 1) 0%, rgba(255, 255, 255, 1) 100%); /* Firefox 3.6+ */
    background: -webkit-gradient(linear, left top, left bottom, color-stop(0%, rgba(173, 216, 230, 1)), color-stop(100%, rgba(255, 255, 255, 1))); /* Safari 4+, Chrome */
    background: -webkit-linear-gradient(0deg, rgba(173, 216, 230, 1) 0%, rgba(255, 255, 255, 1) 100%); /* Safari 5.1+, Chrome 10+ */
    background: -o-linear-gradient(0deg, rgba(173, 216, 230, 1) 0%, rgba(255, 255, 255, 1) 100%); /* Opera 11.10+ */
    background: -ms-linear-gradient(0deg, rgba(173, 216, 230, 1) 0%, rgba(255, 255, 255, 1) 100%); /* IE 10+ */
    background: linear-gradient(0deg, rgba(173, 216, 230, 1) 0%, rgba(255, 255, 255, 1) 100%); /* Standard syntax */
  }
</style>


