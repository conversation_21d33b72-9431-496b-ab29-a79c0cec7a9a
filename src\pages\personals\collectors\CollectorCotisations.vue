<script lang="ts">

  import {defineComponent,onMounted,ref,PropType,toRefs} from 'vue';
  import {get_amount_format,get_date_format} from 'src/helpers/utils';
  import { Subscription } from 'src/models';

  export default defineComponent({
    name: "CollectorCotisations",
    props:{
      cotisations: {
        type: Array as PropType<Subscription[]>,
        required: true
      }
    },
    setup(props){
      const initialPagination = ref({
        sortBy: 'name',
        descending: true,
        page: 1,
        rowsPerPage: 5
      });
      const filter = ref('');
      const loading = ref(false);

      const {cotisations} = toRefs(props);

      const headers = [
        { name: 'id', label: 'ID', field: 'id', sortable: true,align:"left" },
        { name: 'client', label: 'CLIENT', field: 'client', sortable: true,align:"left" },
        { name: 'nbre_cotise', label: 'NBRE COTISATIONS ', field: 'nbre_cotise', sortable: true,align:"left" },
        { name: 'date_cotise', label: 'DERNIERE DATE', field: 'date_cotise', sortable: true,align:"left" },
        { name: 'total_amount', label: 'MONTANT TOTAL', field: 'total_amount', sortable: true,align:"left" },
        { name: 'start_at', label: 'DATE DEBUT', field: 'start_at', sortable: true,align:"left" },
        { name: 'end_at', label: 'DATE FIN', field: 'end_at', sortable: true,align:"left" },
        { name: 'created_at', label: 'DATE CREATION', field: 'created_at', sortable: true,align:"left" },
        { name: 'description', label: 'DESCRIPTION ', field: 'description', sortable: true,align:"left" },
        { name: 'status', label: 'STATUS', field: 'status', sortable: true,align:"left" },
        { name: 'actions', label: 'Actions', field: 'actions', sortable: false },
      ] as any;
      const columns_visibles = [
        'nbre_cotise','date_cotise','client','total_amount','start_at',
        'end_at','status','actions','created_at','description'
      ];

      onMounted(()=>{

        if (cotisations.value.length > 0) {
          loading.value = false;
          console.log("cotisations",cotisations.value);
        }else{
          setTimeout(() => {
            loading.value = false;
          }, 2500);
        }
      });

      return {
        initialPagination, filter, headers,columns_visibles,cotisations,get_amount_format,get_date_format
      };

    }
  });
</script>

<template>
  <div class="q-pa-md">
    <q-table
      flat bordered
      title="Liste des cotisations"
      :rows="cotisations"
      :columns="headers"
      row-key="name"
      :pagination="initialPagination"
      :filter="filter"
      table-style="max-width: 100%;"
      :visible-columns="columns_visibles"
    >
      <template v-slot:top-right="props">
        <q-btn
          flat round dense
          :icon="props.inFullscreen ? 'fullscreen_exit' : 'fullscreen'"
          @click="props.toggleFullscreen"
          class="q-ml-md"
        />
        <q-input filled dense debounce="300" v-model="filter" clearable clear-icon="close" placeholder="Rechercher un versement">
          <template v-slot:append>
            <q-icon name="search" />
          </template>
        </q-input>
      </template>

      <template v-slot:no-data="{ icon, message, }">
        <div class="full-width row flex-center text-accent q-gutter-sm">
          <q-icon size="2em" name="sentiment_dissatisfied" />
          <span>
            Aucune catégorie trouvée
          </span>
          <q-icon size="2em" :name="filter ? 'filter_b_and_w' : icon" />
        </div>
      </template>
      <template v-slot:body="props">
        <q-tr :props="props">
          <q-td key="client" :props="props">
            {{ props.row.client?.nom }} {{ props.row.client?.prenoms }}
          </q-td>
          <q-td key="nbre_cotise" :props="props">
            {{ props.row.nbre_cotise }}
          </q-td>
          <q-td key="date_cotise" :props="props">
            {{ get_date_format(props.row.date_cotise) }}
          </q-td>
          <q-td key="total_amount" :props="props">
            {{ get_amount_format(props.row.total_amount) }}
          </q-td>
          <q-td key="start_at" :props="props">
            {{ get_date_format(props.row.start_at) }}
          </q-td>
          <q-td key="end_at" :props="props">
            {{ get_date_format(props.row.end_at) }}
          </q-td>
          <q-td key="created_at" :props="props">
            {{ get_date_format(props.row.created_at) }}
          </q-td>
          <q-td key="description" :props="props">
            {{ props.row.description?.substr(0,50) }}
          </q-td>
          <q-td key="status" :props="props">
            <q-chip
              :color="props.row.status !== 'confirmed' ? 'warning' : 'positive'"
              text-color="white"
            >
            {{ props.row.status }}
          </q-chip>
          </q-td>

          <q-td key="actions" :props="props">
            <q-btn dense flat color="primary" icon="visibility" class="q-mr-xs" />
          </q-td>
        </q-tr>
      </template>
    </q-table>
  </div>
</template>


