<script lang="ts">
import { storeToRefs } from 'pinia';
import { useQuasar } from 'quasar';
import { User } from 'src/models';
import { adminStore } from 'src/stores/core/adminStore';
import { personalStore } from 'src/stores/core/personalStore';
import { PropType, defineComponent, onMounted, ref, toRefs } from 'vue';

export default defineComponent({
  name: "EditUserSecretCodeModal",
  components: {
  },
  props: {
    user: {
      type: Object as PropType<User>,
      required: true,
      default: () => ({})

    }
  },
  setup(props) {
    const dialog = ref(false);
    const showLoading = ref(false);
    const $q = useQuasar();

    const store = personalStore();
    const {roles} = storeToRefs(adminStore());
    const {  changeCode } = store;

    const { user } = toRefs(props);

    const form = ref({
      user_id: null as unknown as number,
      admin_secret_code: null as unknown as number,
      secret_code: null as unknown as number,
      secret_code_confirmation: null as unknown as number
    });

    const status = ref([
      { label: "Actif", value: "1" },
      { label: "Inactif", value: "0" },
    ]);

    const openDialog = () => {
      if (user.value !== undefined || user.value !== "") {
        form.value.user_id = user.value.id;
        dialog.value = true;
      }
    }

    const generateSecretCode = () => {
      const secretCode = Math.floor(Math.random() * 10000);
      form.value.secret_code = secretCode;
      form.value.secret_code_confirmation = secretCode;
    }

    const onSubmit = async () => {
      console.log(form.value);
      showLoading.value = true;
      try {
        console.log('====================================');
        console.log('form submitted', form.value);
        console.log('====================================');
        const res = await changeCode(form.value);
        console.log('=============res=======================');
        console.log("api response", res);
        console.log('====================================');
        if (res.message) {
          showLoading.value = false;
        }
        if (res.success) {
          showLoading.value = false;
          $q.notify({
            color: "green",
            type: 'positive',
            icon: "check",
            position: 'top-right',
            progress: true,
            timeout: 3500,
            message: res.message
          });
          form.value = {
            user_id: null as unknown as number,
            admin_secret_code: null as unknown as number,
            secret_code: null as unknown as number,
            secret_code_confirmation: null as unknown as number
          };
          dialog.value = false;
        } else {
          $q.notify({
            color: "red",
            type: 'negative',
            icon: "error",
            position: 'top-right',
            progress: true,
            timeout: 3500,
            message: res.message
          });
        }

      } catch (error) {
        console.log("Error : ", error);
        showLoading.value = false;
        $q.notify({
          color: "red-4",
          type: 'negative',
          icon: "error",
          position: 'top-right',
          progress: true,
          timeout: 3500,
          message: "Erreur d'ajout, veuillez réessayer svp"
        });
      }
    };

    const onReset = () => {

    };

    onMounted(async () => {
      // await getRoles();
    });

    return {
      dialog, form, onSubmit, onReset, roles, openDialog, user, showLoading, status,generateSecretCode,
    };
  }
})

</script>
<template>
  <div class="row">
    <q-btn dense flat icon="lock" color="negative" class="q-mr-xs" @click="openDialog" />
    <q-dialog v-model="dialog" persistent full-height transition-show="slide-up" transition-hide="slide-down">
      <q-card >
        <q-form @submit="onSubmit" @reset="onReset">
          <q-toolbar class="bg-primary text-white">
            <q-toolbar-title>
              MODIFIER LE CODE SECRET
            </q-toolbar-title>
            <q-btn flat round dense icon="close" class="q-mr-xs" @click="dialog = false" />
            <!-- <q-btn label="Enregistrer" color="positive" type="submit" /> -->
          </q-toolbar>
          <q-card-section>
          <div class=" bg-warning text-white">
            <b>
              Vous êtes sur le point de modifier le code secret de {{ user.username }}
            </b>
          </div>
          </q-card-section>
          <q-card-section>
            <div class="row q-col-gutter-md">
              <div class="col col-md-12 col-sm-12 col-xs-12">
                <q-input filled v-model="form.admin_secret_code" label="Code secret admin" hint="Code secret admin" lazy-rules
                  :rules="[val => !!val || 'Veuillez entrer votre code secret admin']" />
              </div>
              <div class="col col-md-12 col-sm-12 col-xs-12">
                <q-input filled v-model="form.secret_code" label="Code secret du personnel" hint="Code secret du personnel" lazy-rules
                  :rules="[val => !!val || 'Veuillez saisir ou générer le code secret']" />
              </div>
              <div class="col col-md-12 col-sm-12 col-xs-12">
                <q-input filled v-model="form.secret_code_confirmation" label="Confirmation du code secret" hint="Veuillez confirmer le code secret" lazy-rules
                  :rules="[val => !!val || 'Veuillez confirmer le code secret']" type="text" />
              </div>
              <div class="col col-md-12 col-sm-12 col-xs-12">
                <q-btn color="primary" flat icon="lock" label="GENERER UN CODE" @click="generateSecretCode" />
              </div>
            </div>
          </q-card-section>
          <q-card-actions align="right">
            <q-btn flat label="Annuler" color="negative" @click="dialog = false" />
            <q-btn label="Enregistrer" color="positive" type="submit" />
          </q-card-actions>
          <q-inner-loading :showing="showLoading">
            <q-spinner-gears size="50px" color="primary" />
          </q-inner-loading>
        </q-form>
      </q-card>
    </q-dialog>
  </div>
</template>
