<script lang="ts">
import { storeToRefs } from 'pinia';
import { useQuasar } from 'quasar';
import { productStore } from 'src/stores/core/productStore';
import { defineComponent, ref } from 'vue';

export default defineComponent({
  name: "AddProductModal",
  setup() {
    const $q = useQuasar();
    const dialog = ref(false);
    const showLoading = ref(false);
    const disabled = ref(false);

    const store = productStore();
    const { categories } = storeToRefs(store);
    const { addProduct, getProducts } = store;
    const margin_types = ref([
      { label: "POURCENTAGE", value: "percent" },
      { label: "MONTANT FIXE", value: "amount" },
    ]);

    // Form object for a single product
    const product = ref({
      name: "",
      price_achat: "",
      price_vente: "",
      stock_quantity: "",
      category_id: "",
      profit_margin: "",
      profit_margin_type: "fixed",
      image: null as unknown as File
    });

    const onSubmit = async (evt: Event) => {
      evt.preventDefault();
      disabled.value = true;
      showLoading.value = true;

      try {
        const formData = new FormData();
        for (const key in product.value) {
          const value = product.value[key as keyof typeof product.value];
          formData.append(key, value ?? '');
        }

        const res = await addProduct(formData);
        if (res.success) {
          $q.notify({
            color: "green",
            type: 'positive',
            icon: "cloud_done",
            position: 'top-right',
            progress: true,
            timeout: 3500,
            message: res.message
          });
          product.value = {
            name: "",
            price_achat: "",
            price_vente: "",
            stock_quantity: "",
            category_id: "",
            profit_margin: "",
            profit_margin_type: "fixed",
            image: null as unknown as File
          };

          await getProducts({page:1, limit: 20});
          dialog.value = false;
        } else {
          $q.notify({
            color: "red-4",
            type: 'negative',
            icon: "error",
            position: 'top-right',
            progress: true,
            timeout: 3500,
            message: res.message
          });
        }
      } catch (error) {
        console.log("Error:", error);
        $q.notify({
          color: "red-4",
          type: 'negative',
          icon: "error",
          position: 'top-right',
          progress: true,
          timeout: 3500,
          message: "Une erreur est survenue"
        });
      } finally {
        showLoading.value = false;
        disabled.value = false;
      }
    };

    return {
      dialog, product, onSubmit, categories, showLoading, disabled, margin_types,
    };
  }
});
</script>



<template>
  <div class="row">
    <q-btn color="primary" flat icon="add" label="AJOUTER PRODUIT" @click="dialog = true" />
    <q-dialog v-model="dialog" persistent maximized transition-show="slide-up" transition-hide="slide-down">
      <q-card class="my-card">
        <q-form @submit="onSubmit" enctype="multipart/form-data">
          <q-toolbar class="bg-primary text-white">
            <q-toolbar-title>
              AJOUT D'UN NOUVEAU PRODUIT
            </q-toolbar-title>
            <q-btn flat round dense icon="close" class="q-mr-xs" @click="dialog = false" />
            <q-btn label="Enregistrer" color="positive" type="submit" :disable="disabled" />
          </q-toolbar>
          <q-card-section>
            <div class="row q-col-gutter-md">
              <div class="col col-md-6 col-sm-12 col-xs-12">
                <q-select v-model="product.category_id" :options="categories" label="Catégorie de produit"
                  hint="Catégorie" lazy-rules aria-placeholder="Catégorie" option-label="name" option-value="id"
                  map-options emit-value />
              </div>
              <div class="col col-md-6 col-sm-12 col-xs-12">
                <q-input v-model="product.name" label="Libellé du produit" hint="Libellé du produit" lazy-rules
                  aria-placeholder="Entrez le libellé du produit"
                  :rules="[(val: any) => !!val || 'Veuillez saisir le libellé du produit']" />
              </div>
              <div class="col col-md-6 col-sm-12 col-xs-12">
                <q-input v-model="product.price_achat" label="Prix d'achat" type="number" hint="Prix d'achat du produit"
                  lazy-rules aria-placeholder="Entrez le prix d'achat de ce produit"
                  :rules="[(val: any) => !!val || 'Veuillez saisir le prix d\'achat']" />
              </div>
              <div class="col col-md-6 col-sm-12 col-xs-12">
                <q-input v-model="product.price_vente" label="Prix de vente" type="number" hint="Prix de vente"
                  placeholder="Entrez le prix " />
              </div>
              <div class="col col-md-2 col-sm-12 col-xs-12">
                <q-select v-model="product.profit_margin_type" :options="margin_types"
                  label="Type de marge bénéficiaire" hint="Sélectionnez le type de marge bénéficiaire" lazy-rules
                  aria-placeholder="Sélectionnez le type de marge bénéficiaire" option-label="label"
                  option-value="value" map-options emit-value />
              </div>
              <div class="col col-md-4 col-sm-12 col-xs-12">
                <q-input v-model="product.profit_margin" label="Marge bénéficiaire" type="number"
                  hint="Marge bénéficiaire" lazy-rules placeholder="Entre la valeur du bénéfice sur ce produit" />
              </div>
              <div class="col col-md-6 col-sm-12 col-xs-12">
                <q-input v-model="product.stock_quantity" label="Quantité en stock" hint="Quantité Stock"
                  placeholder="Entrée la quantité en stock disponible pour ce produit" />
              </div>
              <div class="col col-md-12 col-sm-12 col-xs-12">
                <q-file v-model="product.image" label="Image du produit" hint="Image du produit" lazy-rules clearable >
                  <template v-slot:prepend>
                    <q-icon name="attach_file" />
                  </template>
                </q-file>
              </div>
            </div>
          </q-card-section>
          <q-card-actions align="right">
            <q-btn flat label="Annuler" color="primary" @click="dialog = false" />
            <q-btn label="Enregistrer" color="positive" type="submit" />
          </q-card-actions>
          <q-inner-loading :showing="showLoading">
            <q-spinner-gears size="50px" color="primary" />
          </q-inner-loading>
        </q-form>
      </q-card>
    </q-dialog>
  </div>
</template>
