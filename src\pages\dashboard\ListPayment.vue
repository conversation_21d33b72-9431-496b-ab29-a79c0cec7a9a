<script lang="ts">
  import { Cotisation,Payment } from 'src/models';
  import { defineComponent, ref,PropType,toRefs, onMounted } from 'vue';
  import {get_date_format,get_amount_format} from 'src/helpers/utils';
  import { adminStore } from 'src/stores/core/adminStore';
  import { date } from 'quasar';

  export default defineComponent({
    name: "ListPayment",
    setup(){

      const headers = [
        // { name: 'id', label: 'ID', field: 'id', sortable: true,align:"left" },
        // { name: 'agency', label: 'AGENCE ', field: 'agency', sortable: true,align:"left" },
        { name: 'client', label: 'CLIENT', field: 'client', sortable: true,align:"left" },
        { name: 'collector', label: 'AGENT COLLECTEUR', field: 'collector', sortable: true,align:"left" },
        { name: 'pack', label: 'CARNET(PACK)', field: 'pack', sortable: true,align:"left" },
        { name: 'keys', label: 'TOTAL CLES', field: 'keys', sortable: true,align:"left" },
        { name: 'tarif', label: 'TARIF', field: 'tarif', sortable: true,align:"left" },
        { name: 'amount', label: 'MONTANT', field: 'amount', sortable: true,align:"left" },
        { name: 'payment_date', label: 'DATE PAIEMENT', field: 'payment_date', sortable: true,align:"left" },
      ] as any;

      const filter = ref('');
      const filterDialog = ref(false);
      const store = adminStore();
      const {getPaymentByPeriod} = store;
      const loading = ref(true);
      const period = ref("month");
      const periods = ref([
        {label: 'Jour', value: 'day'},
        {label: 'Mois', value: 'month'},
      ])
      const payments = ref<Payment[]>([]);
      const initialPagination = ref({
        sortBy: 'name',
        descending: true,
        page: 1,
        rowsPerPage: 10
      });

      const dateRange = ref({
        from: date.formatDate(date.subtractFromDate(new Date(), { days: 30 }), 'YYYY-MM-DD'),
        to: date.formatDate(new Date(), 'YYYY-MM-DD')
      });


      const filterPaymentByPeriod = async(period:string)=>{
        loading.value = true;
        const res = await getPaymentByPeriod(period);
        console.log("payments",res);
        if (res.message) {
          loading.value = false;
        }
        if (res.success) {
          payments.value = res.result;
        } else {
          console.log("error",res);
        }
      }

      onMounted(async()=>{
        const res = await getPaymentByPeriod(period.value);
        console.log("payments",res);
        if (res.message) {
          loading.value = false;
        }
        if (res.success) {
          payments.value = res.result;
        } else {
          console.log("error",res);
        }
      })

      return {
        get_amount_format,get_date_format,loading,payments,period,initialPagination,
        headers,periods,filterPaymentByPeriod,filterDialog,dateRange
      }

    }
  });

</script>

<template>
  <div class="q-pa-md" style="max-width: 100%">
    <q-table
      flat bordered
      title="Etat de paiements des cotisations"
      :rows="payments"
      :columns="headers"
      :loading="loading"
      row-key="name"
      :pagination="initialPagination"
      table-style="max-width: 100%;"
    >
      <template v-slot:top-right="props">
        <q-select v-model="period" label="Période" dense :options="periods" emit-value map-options option-label="label" options-dense
          class="q-ml-md" style="width: 150px" @update:model-value="filterPaymentByPeriod(period)"
        />
        <q-btn flat color="primary" icon="search"  @click="filterDialog = true" class="q-ml-md" />
        <q-btn
          flat round dense
          :icon="props.inFullscreen ? 'fullscreen_exit' : 'fullscreen'"
          @click="props.toggleFullscreen"
          class="q-ml-md"
        />
      </template>

      <template v-slot:no-data="{ icon, message, }">
        <div class="full-width row flex-center text-accent q-gutter-sm">
          <q-icon size="2em" name="sentiment_dissatisfied" />
          <span>
            Aucune donnée trouvée
          </span>
        </div>
      </template>
      <template v-slot:body="props">
        <q-tr :props="props">
          <q-td key="agency" :props="props">
            {{ props.row.agency?.name }}
          </q-td>
          <q-td key="client" :props="props">
            {{ props.row.client?.nom }} {{ props.row.client?.prenoms }}
          </q-td>
          <q-td key="collector" :props="props">
            {{ props.row.collector?.nom }} {{ props.row.collector?.prenoms }}
          </q-td>
          <q-td key="pack" :props="props">
            {{ props.row.pack?.name }}
          </q-td>
          <q-td key="keys" :props="props">
            {{ props.row.keys  }}
          </q-td>
          <q-td key="tarif" :props="props">
            {{ get_amount_format(props.row.tarif) }}
          </q-td>
          <q-td key="amount" :props="props">
            {{ get_amount_format(props.row.total_amount)  }}
          </q-td>

          <q-td key="payment_date" :props="props">
            {{ get_date_format(props.row.payment_date) }}
          </q-td>


        </q-tr>
      </template>
    </q-table>
  </div>
</template>
