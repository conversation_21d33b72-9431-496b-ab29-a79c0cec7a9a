<script lang="ts">
import { storeToRefs } from 'pinia';
import { adminStore } from 'src/stores/core/adminStore';
import { defineComponent, ref, onMounted } from 'vue';
import { get_amount_format } from 'src/helpers/utils';

export default defineComponent({
  name: "CashierTable",
  setup() {
    const initialPagination = ref({
      sortBy: 'name',
      descending: true,
      page: 1,
      rowsPerPage: 10
    });
    const filter = ref('');
    const loading = ref(false);

    const store = adminStore();
    const { cashiers } = storeToRefs(adminStore());
    const { getCashiers } = store;

    const headers = [
      { name: 'id', label: 'ID', field: 'id', sortable: true, align: "left" },
      { name: 'code_caisse', label: 'CODE CAISSE', field: 'code_caisse', sortable: true, align: "left" },
      { name: 'agency', label: 'AGENCE ', field: 'agency', sortable: true, align: "left" },
      { name: 'cashier', label: 'AGENT CAISSIER', field: 'cashier', sortable: true, align: "left" },
      { name: 'balance', label: 'SOLDE DISPONIBLE', field: 'balance', sortable: true, align: "left" },
      { name: 'total_subscriptions', label: 'MONTANT CARNETS VENDUS', field: 'total_subscriptions', sortable: true, align: "left" },
      { name: 'total_versements', label: 'SOMME TOTAL VERSE', field: 'total_versements', sortable: true, align: "left" },
      { name: 'total_cotisations', label: 'MONTANT TOTAL COTISER', field: 'total_cotisations', sortable: true, align: "left" },
      // { name: 'actions', label: 'ACTIONS', field: 'actions', sortable: false },
    ] as any;
    const columns_visibles = [ 'agency', 'cashier', 'balance', 'total_subscriptions', 'total_versements', 'total_cotisations', 'actions'];


    onMounted(async () => {
      loading.value = true;
      await getCashiers();
      if (cashiers.value.length > 0) {
        loading.value = false;
        console.log("cashiers", cashiers.value);

      } else {
        setTimeout(() => {
          loading.value = false;
        }, 2500);
      }
    });

    return {
      initialPagination, filter, headers, columns_visibles, loading,cashiers, get_amount_format
    };

  }
});
</script>

<template>
  <div class="q-pa-sm">
    <q-table flat bordered title="" :rows="cashiers" :columns="headers" row-key="name"
      :pagination="initialPagination" :filter="filter" table-style="max-width: 100%;"
      :visible-columns="columns_visibles" :loading="loading" >
      <template v-slot:top-right>
        <q-input filled dense debounce="300" v-model="filter" clearable clear-icon="close"
          placeholder="Rechercher une caisse">
          <template v-slot:append>
            <q-icon name="search" />
          </template>
        </q-input>
      </template>

      <template v-slot:no-data="{ icon, message, }">
        <div class="full-width row flex-center text-accent q-gutter-sm">
          <q-icon size="2em" name="sentiment_dissatisfied" />
          <span>
            Aucune donnée disponible trouvée
          </span>
          <q-icon size="2em" :name="filter ? 'filter_b_and_w' : icon" />
        </div>
      </template>
      <template v-slot:body="props">
        <q-tr :props="props">
          <q-td key="code_caisse" :props="props">
            {{ props.row.code_caisse.substr(0, 5) }} .... {{ props.row.code_caisse.substr(5, 5) }}
          </q-td>
          <q-td key="agency" :props="props" >
            <router-link  :to="{ name: 'detail-agency', params: {'code':  props.row.agency?.code } }">
              {{ props.row.agency.name }}
            </router-link>
          </q-td>
          <q-td key="cashier" :props="props">
            {{ props.row.cashier?.nom }} {{ props.row.cashier?.prenoms }}
          </q-td>
          <q-td key="balance" :props="props">
            {{ get_amount_format(props.row.balance) }}
          </q-td>
          <q-td key="total_subscriptions" :props="props">
            {{ get_amount_format(props.row.total_subscriptions) }}
          </q-td>
          <q-td key="total_versements" :props="props">
            {{ get_amount_format(props.row.total_versements) }}
          </q-td>
          <q-td key="total_cotisations" :props="props">
            <q-chip class="glossy"   >
              {{ get_amount_format(props.row.total_cotisations) }}
            </q-chip>
          </q-td>

          <q-td key="actions" :props="props">
            <q-btn dense flat color="primary" icon="visibility" class="q-mr-xs" />
          </q-td>
        </q-tr>
      </template>
    </q-table>
  </div>
</template>
