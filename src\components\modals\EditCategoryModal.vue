<script lang="ts">
  import { defineComponent, ref, toRefs } from 'vue';
  import { productStore } from 'src/stores/core/productStore';
  import { useQuasar } from 'quasar';
  export default defineComponent({
    name: "EditCategoryModal",
    props: {
      category: {
        type: Object,
        required: true
      }
    },
    setup(props){
      const $q = useQuasar();
      const dialog = ref(false);
      const loading = ref(false);
      const {category} = toRefs(props);

      const form = ref({
        category_id: 0,
        name: "",
        description: ""
      });

      const openDialog = ()=> {
        if (category.value  !== undefined || category.value !== "") {
          dialog.value = true;
          form.value = {
            category_id: category.value.id,
            name: category.value.name,
            description: category.value.description
          }
        }
      };

      const store = productStore();
      const {getCategories,updateCategory} = store;

      const onSubmit = async()=>{
        loading.value = true;
        try {
          console.log(form.value);
          const res = await updateCategory(form.value);
          console.log('====================================');
          console.log('res', res);
          console.log('====================================');
          if (res.success) {
            $q.notify({
              color: "green",
              type: 'positive',
              icon: "check",
              position: 'top-right',
              progress: true,
              timeout: 3500,
              message: res.message
            });

            form.value = {
              category_id: 0,
              name: "",
              description: ""
            };
            const new_res = await getCategories();
            if (new_res.success) {
              loading.value = false;
              dialog.value = false;
            }
          }else{
            $q.notify({
              color: "red-4",
              type: 'negative',
              icon: "error",
              position: 'top-right',
              progress: true,
              timeout: 3500,
              message: res.message
            });
            loading.value = false;
          }
        } catch (error) {
          console.log("Error : ", error );
          loading.value = false;
          $q.notify({
            color: "red-4",
            type: 'negative',
            icon: "error",
            position: 'top-right',
            progress: true,
            timeout: 3500,
            message: "Erreur lors de l'ajout des categories"
          });
        }
      };

      const onReset = ()=>{

      };

      return {
        dialog, form,onSubmit,onReset,loading,openDialog,category
      };

    }
  })

</script>
<template>
  <div class="">
    <q-btn dense flat icon="edit" color="secondary" class="q-mr-xs"  @click="openDialog" />
    <q-dialog v-model="dialog" full-height full-width persistent transition-show="slide-up" transition-hide="slide-down">
      <q-card>
        <q-form  @submit="onSubmit" @reset="onReset">
          <q-toolbar class="bg-primary text-white">
            <q-toolbar-title>
              Modifier la catégorie {{ category.name }}
            </q-toolbar-title>
            <q-btn flat round dense icon="close" class="q-mr-xs" @click="dialog = false" />
            <q-btn  label="Enregistrer" color="positive" type="submit" />
          </q-toolbar>
          <q-card-section>
            <q-scroll-area style="width: ; height: 430px;">
              <div class="row q-col-gutter-md">
                <div class="col col-md-12 col-sm-12 col-xs-12">
                  <q-input filled v-model="form.name" label="Nom de la catégorie"
                    hint="Nom de la catégorie" lazy-rules placeholder="Entrez le Nom de la catégorie"
                    :rules="[val => !!val || 'Veuillez saisir le nom de la catégorie']"
                  />
                </div>
                <div class="col col-md-12 col-sm-12 col-xs-12">
                  <q-input filled  v-model="form.description" label="Description de la catégorie" type="textarea"
                    hint="Description de la catégorie" lazy-rules placeholder="Entrez la description de la catégorie" autogrow
                  />
                </div>
              </div>
            </q-scroll-area>
          </q-card-section>
          <q-card-actions align="right">
            <q-btn flat label="Annuler" color="primary" @click="dialog = false" />
            <q-btn  label="Enregistrer" color="positive" type="submit" />
          </q-card-actions>
          <q-inner-loading :showing="loading">
            <q-spinner-gears size="50px" color="primary" />
          </q-inner-loading>
        </q-form>
      </q-card>
    </q-dialog>
  </div>
</template>
